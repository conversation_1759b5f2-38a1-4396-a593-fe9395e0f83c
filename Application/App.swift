import SwiftUI
import SwiftData
@preconcurrency import <PERSON>baseCore
@preconcurrency import GoogleSignIn

@main
struct IngredientScannerApp: App {
    @State private var serviceContainer: ServiceContainer
    private let swiftDataStorage = SwiftDataStorageService.shared
    
    init() {
        // Configure Firebase FIRST - this must happen before any Firebase services are accessed
        FirebaseApp.configure()
        
        // Configure Google Sign-In using Firebase default app options.clientID (per Firebase docs)
        if let clientId = FirebaseApp.app()?.options.clientID {
            GIDSignIn.sharedInstance.configuration = GIDConfiguration(clientID: clientId)
            print("✅ Firebase and Google Sign-In configured successfully")
        } else {
            print("⚠️ Failed to read clientID from FirebaseApp options")
        }
        
        // Initialize ServiceContainer AFTER Firebase is fully configured
        serviceContainer = ServiceContainer.shared
    }
    
    var body: some Scene {
        WindowGroup {
            // Use the RootView with proper service injection
            RootView(pantryService: serviceContainer.pantryService)
                .environment(serviceContainer.pantryService)
                .environment(serviceContainer.authenticationService)
                .environment(swiftDataStorage)
                .onOpenURL { url in
                    // Handle Google Sign-In URL callbacks as per Firebase documentation
                    _ = GIDSignIn.sharedInstance.handle(url)
                }
        }
        .modelContainer(swiftDataStorage.container ?? createFallbackContainer())
    }
    
    /// Create fallback ModelContainer if SwiftDataStorageService fails
    private func createFallbackContainer() -> ModelContainer {
        do {
            let schema = Schema([
                SavedRecipe.self,
                SavedIngredient.self,
                SavedUserPreferences.self,
                ShoppingListItem.self,
                RecipeHistory.self
            ])
            
            let modelConfiguration = ModelConfiguration(
                schema: schema,
                isStoredInMemoryOnly: true
            )
            
            return try ModelContainer(
                for: schema,
                configurations: [modelConfiguration]
            )
        } catch {
            fatalError("Failed to create fallback ModelContainer: \(error)")
        }
    }
}
