import SwiftUI
import UIKit

// MARK: - Unified AppRoute Enum for Navigation

/// Unified navigation route enum that encompasses all possible navigation destinations
/// Implements Hashable for use with NavigationStack and programmatic navigation
enum AppRoute: Hashable {
    // Scanner flow routes
    case staging
    
    // MARK: - Deprecated Routes (Maintained for Backward Compatibility)
    // These routes are deprecated in favor of direct staging->results flow
    // They will be removed in a future version after migration is complete
    
    @available(*, deprecated, message: "Use staging with direct processing instead. This route will be removed in a future version.")
    case batchProcessing(images: [UIImage])
    
    @available(*, deprecated, message: "Use results instead. Vision processing is now handled internally in staging.")
    case batchVisionResults(images: [UIImage], visionResponses: [String])
    
    @available(*, deprecated, message: "Use results instead. Gemini processing is now handled internally in staging.")
    case batchGeminiProcessing(images: [UIImage], visionResponses: [String])
    
    // MARK: - Current Routes
    case results(ingredients: [Ingredient])
    
    // Debug route (only available in debug builds)
    #if DEBUG
    case debug(visionResponse: String, geminiResponse: String, ingredients: [Ingredient])
    #endif
    
    // Profile routes
    case profile
    case signIn
    case preferencesEdit
    
    // Recipe routes
    case recipeGenerator
    case recipeDetail(recipe: Recipe)
    case ingredientsView(recipe: Recipe)
    

    // MARK: - Hashable Implementation
    
    func hash(into hasher: inout Hasher) {
        switch self {
        case .staging:
            hasher.combine("staging")
            
        case .batchProcessing(let images):
            hasher.combine("batchProcessing")
            hasher.combine(images.count)
            // Use a lightweight hash based on image count and first few image properties
            for (index, image) in images.prefix(3).enumerated() {
                hasher.combine(index)
                hasher.combine(image.size.width)
                hasher.combine(image.size.height)
                hasher.combine(image.scale)
            }
            
        case .batchVisionResults(let images, let visionResponses):
            hasher.combine("batchVisionResults")
            hasher.combine(images.count)
            hasher.combine(visionResponses.count)
            // Hash first few responses for uniqueness
            for response in visionResponses.prefix(2) {
                hasher.combine(response.prefix(50)) // Use first 50 chars for efficiency
            }
            
        case .batchGeminiProcessing(let images, let visionResponses):
            hasher.combine("batchGeminiProcessing")
            hasher.combine(images.count)
            hasher.combine(visionResponses.count)
            for response in visionResponses.prefix(2) {
                hasher.combine(response.prefix(50))
            }
            
        case .results(let ingredients):
            hasher.combine("results")
            hasher.combine(ingredients.count)
            // Hash first few ingredient IDs for uniqueness
            for ingredient in ingredients.prefix(5) {
                hasher.combine(ingredient.id)
            }
            
        #if DEBUG
        case .debug(let visionResponse, let geminiResponse, let ingredients):
            hasher.combine("debug")
            hasher.combine(visionResponse.prefix(50))
            hasher.combine(geminiResponse.prefix(50))
            hasher.combine(ingredients.count)
        #endif
            
        case .profile:
            hasher.combine("profile")
            
        case .signIn:
            hasher.combine("signIn")
            
        case .preferencesEdit:
            hasher.combine("preferencesEdit")
            
        case .recipeGenerator:
            hasher.combine("recipeGenerator")
            
        case .recipeDetail(let recipe):
            hasher.combine("recipeDetail")
            hasher.combine(recipe.id)
            
        case .ingredientsView(let recipe):
            hasher.combine("ingredientsView")
            hasher.combine(recipe.id)
            
        }
    }
    
    // MARK: - Equatable Implementation
    
    static func == (lhs: AppRoute, rhs: AppRoute) -> Bool {
        switch (lhs, rhs) {
        case (.staging, .staging):
            return true
            
        case (.batchProcessing(let lhsImages), .batchProcessing(let rhsImages)):
            return lhsImages.count == rhsImages.count &&
                   zip(lhsImages, rhsImages).allSatisfy { lhsImg, rhsImg in
                       lhsImg.size == rhsImg.size && lhsImg.scale == rhsImg.scale
                   }
            
        case (.batchVisionResults(let lhsImages, let lhsResponses), 
              .batchVisionResults(let rhsImages, let rhsResponses)):
            return lhsImages.count == rhsImages.count &&
                   lhsResponses == rhsResponses
            
        case (.batchGeminiProcessing(let lhsImages, let lhsResponses),
              .batchGeminiProcessing(let rhsImages, let rhsResponses)):
            return lhsImages.count == rhsImages.count &&
                   lhsResponses == rhsResponses
            
        case (.results(let lhsIngredients), .results(let rhsIngredients)):
            return lhsIngredients == rhsIngredients
            
        #if DEBUG
        case (.debug(let lhsVision, let lhsGemini, let lhsIngredients),
              .debug(let rhsVision, let rhsGemini, let rhsIngredients)):
            return lhsVision == rhsVision &&
                   lhsGemini == rhsGemini &&
                   lhsIngredients == rhsIngredients
        #endif
            
        case (.profile, .profile),
             (.signIn, .signIn),
             (.preferencesEdit, .preferencesEdit),
             (.recipeGenerator, .recipeGenerator):
            return true

        case (.recipeDetail(let lhsRecipe), .recipeDetail(let rhsRecipe)):
            return lhsRecipe.id == rhsRecipe.id
            
        case (.ingredientsView(let lhsRecipe), .ingredientsView(let rhsRecipe)):
            return lhsRecipe.id == rhsRecipe.id
            
        default:
            return false
        }
    }
}

// MARK: - NavigationCoordinator with NavigationPath

/// Modern NavigationCoordinator class using @Observable and NavigationPath for iOS 17
/// Implements unified navigation management with AppRoute enum
@Observable
@MainActor
final class NavigationCoordinator {
    /// Single NavigationPath for unified navigation management
    var path = NavigationPath()
    
    /// Tab selection state
    var tabSelection: Int = 0
    
    /// Navigation state for restoration
    private var navigationStateData: Data?
    
    // MARK: - Core Navigation Methods
    
    /// Push a new route onto the navigation stack
    func navigate(to route: AppRoute) {
        path.append(route)
    }
    
    /// Pop back to the root of the navigation stack
    func popToRoot() {
        if !path.isEmpty {
            path.removeLast(path.count)
        }
    }
    
    /// Pop back n levels from the navigation stack
    func popLevels(_ count: Int) {
        guard count > 0 && count <= path.count else { return }
        path.removeLast(count)
    }
    
    /// Pop back one level (convenience method)
    func popBack() {
        if !path.isEmpty {
            path.removeLast()
        }
    }
    
    /// Replace the current route with a new one
    func replaceCurrent(with route: AppRoute) {
        if !path.isEmpty {
            path.removeLast()
        }
        path.append(route)
    }
    
    /// Clear the entire navigation stack
    func clearPath() {
        path = NavigationPath()
    }
    
    // MARK: - Convenience Navigation Methods
    
    /// Navigate to staging view (scanner root)
    func navigateToStaging() {
        navigate(to: .staging)
    }
    
    // MARK: - Deprecated Navigation Methods (Maintained for Backward Compatibility)
    
    /// Navigate to batch processing with images
    @available(*, deprecated, message: "Use direct processing in StagingView instead. This method will be removed in a future version.")
    func navigateToBatchProcessing(images: [UIImage]) {
        navigate(to: .batchProcessing(images: images))
    }
    
    /// Navigate to batch vision results
    @available(*, deprecated, message: "Vision processing is now handled internally in StagingView. This method will be removed in a future version.")
    func navigateToBatchVisionResults(images: [UIImage], visionResponses: [String]) {
        navigate(to: .batchVisionResults(images: images, visionResponses: visionResponses))
    }
    
    /// Navigate to batch Gemini processing
    @available(*, deprecated, message: "Gemini processing is now handled internally in StagingView. This method will be removed in a future version.")
    func navigateToBatchGeminiProcessing(images: [UIImage], visionResponses: [String]) {
        navigate(to: .batchGeminiProcessing(images: images, visionResponses: visionResponses))
    }
    
    // MARK: - Current Navigation Methods
    
    /// Navigate to results view with processed ingredients
    /// This is the primary method for navigation after processing is complete
    func navigateToResults(ingredients: [Ingredient]) {
        navigate(to: .results(ingredients: ingredients))
    }
    
    // MARK: - Enhanced Processing Flow Support
    
    /// Handle processing results with error handling and partial success support
    /// This method manages the transition from processing to results or error states
    func handleProcessingResults<T>(_ results: [Result<T, Error>], transform: (T) -> Ingredient) {
        let successfulIngredients = results.compactMap { result in
            switch result {
            case .success(let value):
                return transform(value)
            case .failure(_):
                return nil
            }
        }
        
        let failureCount = results.count - successfulIngredients.count
        
        if !successfulIngredients.isEmpty {
            // Navigate to results with successful ingredients
            navigateToResults(ingredients: successfulIngredients)
            
            // TODO: Show partial success notification if there were failures
            if failureCount > 0 {
                // This could be handled by the calling view model to show appropriate UI feedback
                print("⚠️ Processed \(successfulIngredients.count) of \(results.count) items successfully")
            }
        } else {
            // Handle complete failure case
            // The calling view model should handle this by showing error UI
            print("❌ Processing failed for all items")
        }
    }
    
    /// Handle processing results specifically for ingredients (convenience method)
    func handleIngredientProcessingResults(_ results: [Result<Ingredient, Error>]) {
        handleProcessingResults(results) { $0 }
    }
    
    #if DEBUG
    /// Navigate to debug view (debug builds only)
    func navigateToDebug(visionResponse: String, geminiResponse: String, ingredients: [Ingredient]) {
        navigate(to: .debug(visionResponse: visionResponse, geminiResponse: geminiResponse, ingredients: ingredients))
    }
    #endif
    
    /// Navigate to profile view
    func navigateToProfile() {
        navigate(to: .profile)
    }
    
    /// Navigate to sign in view
    func navigateToSignIn() {
        navigate(to: .signIn)
    }
    
    /// Navigate to preferences edit view
    func navigateToPreferencesEdit() {
        navigate(to: .preferencesEdit)
    }
    
    /// Navigate to recipe generator
    func navigateToRecipeGenerator() {
        navigate(to: .recipeGenerator)
    }
    
    /// Navigate to recipe detail
    func navigateToRecipeDetail(recipe: Recipe) {
        navigate(to: .recipeDetail(recipe: recipe))
    }
    
    /// Navigate to ingredients view for a recipe
    func navigateToIngredientsView(recipe: Recipe) {
        navigate(to: .ingredientsView(recipe: recipe))
    }
    
    /// Navigate to settings

    // MARK: - Tab Management
    
    /// Switch to a specific tab
    func switchToTab(_ tabIndex: Int) {
        guard tabIndex >= 0 && tabIndex <= 3 else { return }
        tabSelection = tabIndex
    }
    
    /// Switch to pantry tab
    func switchToPantryTab() {
        switchToTab(1)
    }
    
    /// Switch to pantry tab and reset scan navigation
    func switchToPantryTabAndResetScan() {
        switchToTab(1)
        // Reset navigation path after a brief delay
        Task { @MainActor in
            try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
            popToRoot()
        }
    }
    
    /// Switch to profile tab
    func switchToProfileTab() {
        switchToTab(3)
    }
    
    /// Reset scan tab to staging
    func resetScanTab() {
        popToRoot()
        navigateToStaging()
    }
    
    // MARK: - Enhanced Flow Support
    
    /// Reset the entire scan flow and clear any processing state
    /// This method is designed for the new direct processing flow
    func resetScanFlow() {
        // Switch to scan tab (tab 0)
        switchToTab(0)
        
        // Clear navigation stack
        popToRoot()
        
        // The StagingView will handle clearing its own state when it appears
        // This ensures a clean slate for the next scanning session
    }
    
    // MARK: - Deep Linking Support
    
    /// Handle deep link URLs
    func handleDeepLink(_ url: URL) {
        // Parse URL components
        guard let components = URLComponents(url: url, resolvingAgainstBaseURL: false),
              let scheme = components.scheme,
              scheme == "ingredientscanner" else {
            return
        }
        
        // Clear current path for deep link navigation
        clearPath()
        
        // Handle different deep link paths
        switch components.path {
        case "/staging":
            navigateToStaging()
        case "/profile":
            switchToProfileTab()
            navigateToProfile()
        case "/recipes":
            switchToTab(2)
            navigateToRecipeGenerator()
        case "/pantry":
            switchToPantryTab()
            
        // MARK: - Deprecated Route Handling (Maintained for Backward Compatibility)
        // These routes redirect to the new direct processing flow
        
        case "/batch-processing", "/batchProcessing":
            // Redirect deprecated batch processing deep links to staging
            print("⚠️ Deep link '/batch-processing' is deprecated. Redirecting to staging with direct processing.")
            navigateToStaging()
            
        case "/batch-vision", "/batchVision":
            // Redirect deprecated vision results deep links to staging
            print("⚠️ Deep link '/batch-vision' is deprecated. Redirecting to staging with direct processing.")
            navigateToStaging()
            
        case "/batch-gemini", "/batchGemini":
            // Redirect deprecated Gemini processing deep links to staging
            print("⚠️ Deep link '/batch-gemini' is deprecated. Redirecting to staging with direct processing.")
            navigateToStaging()
            
        default:
            // Default to staging if path is not recognized
            navigateToStaging()
        }
    }
    
    // MARK: - State Restoration
    
    /// Encode the current navigation state for persistence
    func encodeNavigationState() -> Data? {
        do {
            let encoder = JSONEncoder()
            let state = NavigationState(
                pathCount: path.count,
                tabSelection: tabSelection
            )
            return try encoder.encode(state)
        } catch {
            print("Failed to encode navigation state: \(error)")
            return nil
        }
    }
    
    /// Restore navigation state from saved data
    func restoreNavigationState(from data: Data) {
        do {
            let decoder = JSONDecoder()
            let state = try decoder.decode(NavigationState.self, from: data)
            
            // Restore tab selection
            tabSelection = state.tabSelection
            
            // Note: NavigationPath doesn't support direct restoration of complex routes
            // In a real implementation, you would need to store route information separately
            // and reconstruct the path based on that information
            
        } catch {
            print("Failed to restore navigation state: \(error)")
        }
    }
    
    // MARK: - Navigation State Information
    
    /// Get the current depth of the navigation stack
    var navigationDepth: Int {
        return path.count
    }
    
    /// Check if the navigation stack is empty
    var isAtRoot: Bool {
        return path.isEmpty
    }
    
    /// Check if we can navigate back
    var canNavigateBack: Bool {
        return !path.isEmpty
    }
}

// MARK: - Navigation State for Persistence

private struct NavigationState: Codable {
    let pathCount: Int
    let tabSelection: Int
}

// MARK: - Legacy Destination Enums (for backward compatibility)









// MARK: - Task 33: Root NavigationStack Implementation

/// RootView implementing the root NavigationStack as specified in Task 33
/// This replaces the current navigation system with a unified NavigationStack approach
struct RootView: View {
    @State private var coordinator = NavigationCoordinator()
    private let pantryService: PantryService
    
    init(pantryService: PantryService) {
        self.pantryService = pantryService
    }
    
    var body: some View {
        TabView(selection: $coordinator.tabSelection) {
            // Home/Scanner Tab - Main NavigationStack with all destinations
            NavigationStack(path: $coordinator.path) {
                StagingView(viewModel: StagingViewModel(navigationCoordinator: coordinator))
                    .navigationDestination(for: AppRoute.self) { route in
                        destinationView(for: route)
                    }
            }
            .tabItem { 
                Label("Scan", systemImage: "barcode.viewfinder") 
            }
            .tag(0)
            
            // Pantry Tab - Independent NavigationStack
            NavigationStack {
                PantryView()
                    .navigationDestination(for: AppRoute.self) { route in
                        destinationView(for: route)
                    }
            }
            .tabItem { 
                Label("Pantry", systemImage: "cabinet.fill") 
            }
            .tag(1)
            
            // Recipe Tab - Independent NavigationStack
            NavigationStack {
                RecipeGeneratorView()
                    .navigationDestination(for: AppRoute.self) { route in
                        destinationView(for: route)
                    }
            }
            .tabItem { 
                Label("Recipes", systemImage: "book.closed.fill") 
            }
            .tag(2)
            
            // Profile Tab - Independent NavigationStack
            NavigationStack {
                ProfileView()
                    .navigationDestination(for: AppRoute.self) { route in
                        destinationView(for: route)
                    }
            }
            .tabItem { 
                Label("Profile", systemImage: "person.fill") 
            }
            .tag(3)
        }
        // CRITICAL: Environment must be injected at TabView level to reach all tabs
        .environment(pantryService)
        .environment(ServiceContainer.shared.authenticationService)
        .environment(coordinator)
        .onOpenURL { url in
            coordinator.handleDeepLink(url)
        }
    }
    
    // MARK: - Unified Destination View Builder
    
    @ViewBuilder
    private func destinationView(for route: AppRoute) -> some View {
        switch route {
        // Scanner flow routes
        case .staging:
            StagingView(viewModel: StagingViewModel(navigationCoordinator: coordinator))
            
                case .batchProcessing(_):
            // Deprecated: Redirect to StagingView with direct processing
            StagingView(viewModel: StagingViewModel(navigationCoordinator: coordinator))
            
        case .batchVisionResults(_, _):
            // Deprecated: Redirect to StagingView with direct processing
            StagingView(viewModel: StagingViewModel(navigationCoordinator: coordinator))
            
        case .batchGeminiProcessing(_, _):
            // Deprecated: Redirect to StagingView with direct processing
            StagingView(viewModel: StagingViewModel(navigationCoordinator: coordinator))
            
        case .results(let ingredients):
            ResultsView(ingredients: ingredients)
            
        #if DEBUG
        case .debug(let visionResponse, let geminiResponse, let ingredients):
            DebugView(viewModel: DebugViewModel(navigationCoordinator: coordinator, visionResponse: visionResponse, geminiResponse: geminiResponse, ingredients: ingredients))
        #endif
            
        // Profile routes
        case .profile:
            ProfileView()
            
        case .signIn:
            SignInView()
            
        case .preferencesEdit:
            PreferencesEditView()
            
        // Recipe routes
        case .recipeGenerator:
            RecipeGeneratorView()
            
        case .recipeDetail(let recipe):
            GeneratedRecipeDetailView(recipe: recipe)
            
        case .ingredientsView(let recipe):
            // Future implementation for recipe ingredients view
            VStack {
                Text("Ingredients for \(recipe.title)")
                    .font(.title2)
                    .padding()
                
                Text("This view will show detailed ingredient information")
                    .foregroundColor(.secondary)
            }
            
        }
    }
}

