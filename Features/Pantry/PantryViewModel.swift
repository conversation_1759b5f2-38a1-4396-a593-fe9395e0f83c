import Foundation
import SwiftUI

@Observable
@MainActor
class PantryViewModel {
    private let pantryService: PantryService
    private var organizerService: PantryOrganizerService?
    var searchText: String = ""
    var showingAddIngredient = false
    var newIngredientName = ""
    var selectedCategory: PantryCategory = .other
    var isEditMode = false
    var selectedItems: Set<UUID> = []
    var showDeleteConfirmation = false

    // Organization-related properties
    var isOrganizing = false
    var organizerSummary: PantryOrganizerSummary? = nil
    var organizerError: Error? = nil
    
    // 🔥 SNAPSHOT MECHANISM: Capture selected items when delete is initiated
    private var deleteSnapshot: Set<UUID> = []
    private var deleteSnapshotNames: String = ""
    
    // Editing UI disabled per request
    var showingEditIngredient = false
    var editingIngredient: Ingredient? = nil
    var editIngredientName = ""
    var editIngredientCategory: PantryCategory = .other
    var sortMode: SortMode = .oldToNew

    // Computed property to get recently added items
    var recentlyAddedItems: [Ingredient] {
        return pantryService.pantryItems.filter { pantryService.isRecentlyAdded($0) }
    }

    // Computed property to get filtered items based on search
    var filteredPantryItems: [Ingredient] {
        if searchText.isEmpty { return sortedPantryItems }
        // Restrict to library names only and prefix match
        let allowed = Set(IngredientLibrary.shared.allIngredientNames.map { $0.lowercased() })
        let q = searchText.lowercased()
        return sortedPantryItems.filter { item in
            let nameLower = item.name.lowercased()
            return allowed.contains(nameLower) && nameLower.hasPrefix(q)
        }
    }

    // Sorted items by date (default: old to new)
    var sortedPantryItems: [Ingredient] {
        switch sortMode {
        case .oldToNew:
            return pantryService.pantryItems.sorted { $0.dateAdded < $1.dateAdded }
        case .newToOld:
            return pantryService.pantryItems.sorted { $0.dateAdded > $1.dateAdded }
        case .nameAZ:
            return pantryService.pantryItems.sorted { $0.name.localizedCaseInsensitiveCompare($1.name) == .orderedAscending }
        case .nameZA:
            return pantryService.pantryItems.sorted { $0.name.localizedCaseInsensitiveCompare($1.name) == .orderedDescending }
        case .categoryThenName:
            return pantryService.pantryItems.sorted { lhs, rhs in
                if lhs.category == rhs.category {
                    return lhs.name.localizedCaseInsensitiveCompare(rhs.name) == .orderedAscending
                }
                // Use display order of categories
                let order: [PantryCategory] = [
                    .produce, .proteins, .dairy, .plantBasedAlternatives, .bakery,
                    .grainsPastaLegumes, .spicesAndSeasonings, .oilsVinegarsAndCondiments,
                    .cannedAndBroths, .nutsAndSeeds, .snacks, .bakingAndSweeteners, .other
                ]
                return (order.firstIndex(of: lhs.category) ?? 999) < (order.firstIndex(of: rhs.category) ?? 999)
            }
        }
    }

    enum SortMode: String, CaseIterable, Identifiable {
        case oldToNew = "Old → New"
        case newToOld = "New → Old"
        case nameAZ = "Name A–Z"
        case nameZA = "Name Z–A"
        case categoryThenName = "Category, then Name"
        var id: String { rawValue }
    }

    // Computed property to group pantry items by category
    var categorizedPantryItems: [(category: PantryCategory, ingredients: [Ingredient])] {
        let grouped = Dictionary(grouping: filteredPantryItems) { $0.category }

        // Sort categories in a logical order (same as ResultsViewModel)
        let categoryOrder: [PantryCategory] = [
            .produce,
            .proteins,
            .dairy,
            .plantBasedAlternatives,
            .bakery,
            .grainsPastaLegumes,
            .spicesAndSeasonings,
            .oilsVinegarsAndCondiments,
            .cannedAndBroths,
            .nutsAndSeeds,
            .snacks,
            .bakingAndSweeteners,
            .other
        ]

        return categoryOrder.compactMap { category in
            guard let items = grouped[category], !items.isEmpty else { return nil }
            return (category: category, ingredients: items)
        }
    }

    init(pantryService: PantryService, organizerService: PantryOrganizerService? = nil) {
        self.pantryService = pantryService
        self.organizerService = organizerService
    }
    
    func deleteIngredient(_ ingredient: Ingredient, from category: PantryCategory) {
        if let index = pantryService.pantryItems.firstIndex(where: { $0.id == ingredient.id }) {
            pantryService.pantryItems.remove(at: index)
        }
    }

    // MARK: - Manual Add Functionality

    func addIngredient() {
        let trimmedName = newIngredientName.trimmingCharacters(in: .whitespacesAndNewlines)
        if !trimmedName.isEmpty {
            let newIngredient = Ingredient(name: trimmedName, category: selectedCategory)
            Task {
                await pantryService.addIngredient(newIngredient)
            }
            newIngredientName = ""
            selectedCategory = .other
            showingAddIngredient = false
        }
    }

    func addIngredients(_ ingredients: [Ingredient]) {
        Task { await pantryService.addIngredients(ingredients) }
    }

    func cancelAddIngredient() {
        newIngredientName = ""
        selectedCategory = .other
        showingAddIngredient = false
    }

    // MARK: - Edit Mode & Bulk Actions

    func toggleEditMode() {
        isEditMode.toggle()
        if !isEditMode {
            selectedItems.removeAll()
        }
    }

    // 🔥 SIMPLIFIED: Only keep UUID-based item selection to avoid confusion
    func toggleItemSelection(_ itemId: UUID) {
        let beforeCount = selectedItems.count
        let beforeItems = selectedItems
        
        if selectedItems.contains(itemId) {
            selectedItems.remove(itemId)
        } else {
            selectedItems.insert(itemId)
        }
        
        let afterCount = selectedItems.count
        let afterItems = selectedItems
        
        // 🔍 DETAILED DEBUG: Track every change to selectedItems
        print("🔍 DEBUG: toggleItemSelection called")
        print("🔍 DEBUG: itemId = \(itemId)")
        print("🔍 DEBUG: before count = \(beforeCount)")
        print("🔍 DEBUG: after count = \(afterCount)")
        print("🔍 DEBUG: before items = \(beforeItems)")
        print("🔍 DEBUG: after items = \(afterItems)")
        print("🔍 DEBUG: pantryService.pantryItems.count = \(pantryService.pantryItems.count)")
        
        // 🔍 DEBUG: Print call stack
        print("🔍 DEBUG: toggleItemSelection call stack:")
        Thread.callStackSymbols.prefix(3).forEach { symbol in
            print("  \(symbol)")
        }
    }

    func isItemSelected(_ ingredient: Ingredient) -> Bool {
        return selectedItems.contains(ingredient.id)
    }

    func selectAllItems() {
        let beforeCount = selectedItems.count
        selectedItems = Set(filteredPantryItems.map { $0.id })
        let afterCount = selectedItems.count
        print("🔍 DEBUG: selectAllItems called - before: \(beforeCount), after: \(afterCount), filteredItems: \(filteredPantryItems.count)")
        
        // 🔍 DEBUG: Print call stack to identify unexpected calls
        print("🔍 DEBUG: selectAllItems call stack:")
        Thread.callStackSymbols.prefix(5).forEach { symbol in
            print("  \(symbol)")
        }
    }

    func deselectAllItems() {
        selectedItems.removeAll()
    }

    // 🔥 SNAPSHOT MECHANISM: Prepare deletion with isolated state
    func prepareDeletion() {
        print("🔍 DEBUG: prepareDeletion called")
        print("🔍 DEBUG: selectedItems.count = \(selectedItems.count)")
        print("🔍 DEBUG: selectedItems = \(selectedItems)")
        
        // 🛡️ Capture snapshot of current selection
        deleteSnapshot = selectedItems
        
        // 🛡️ Capture names immediately to prevent any changes
        let selectedIngredients = pantryService.pantryItems.filter { deleteSnapshot.contains($0.id) }
        deleteSnapshotNames = selectedIngredients.map { $0.name }.joined(separator: ", ")
        
        print("🔍 DEBUG: deleteSnapshot.count = \(deleteSnapshot.count)")
        print("🔍 DEBUG: deleteSnapshotNames = \(deleteSnapshotNames)")
        
        showDeleteConfirmation = true
    }
    
    // 🔥 SNAPSHOT MECHANISM: Get snapshot count for UI
    func getDeleteSnapshotCount() -> Int {
        return deleteSnapshot.count
    }
    
    // 🔥 SNAPSHOT MECHANISM: Get snapshot names for UI
    func getDeleteSnapshotNames() -> String {
        return deleteSnapshotNames
    }

    // 🔥 IMPROVED: Get names of selected items for confirmation dialog
    func getSelectedItemNames() -> String {
        // 🔍 DETAILED DEBUG: Track what's happening in confirmation dialog
        print("🔍 DEBUG: getSelectedItemNames called")
        print("🔍 DEBUG: selectedItems.count = \(selectedItems.count)")
        print("🔍 DEBUG: selectedItems = \(selectedItems)")
        print("🔍 DEBUG: pantryService.pantryItems.count = \(pantryService.pantryItems.count)")
        
        let selectedIngredients = pantryService.pantryItems.filter { selectedItems.contains($0.id) }
        let itemNames = selectedIngredients.map { $0.name }.joined(separator: ", ")
        
        print("🔍 DEBUG: selectedIngredients.count = \(selectedIngredients.count)")
        print("🔍 DEBUG: selectedIngredients names = \(itemNames)")
        
        return itemNames
    }

    // 🔥 COMPLETE REWRITE: Simple, direct deletion method using snapshot
    func deleteSelectedItemsNow() {
        print("🔥 SIMPLE DELETE: Using snapshot - deleting \(deleteSnapshot.count) items")
        print("🔥 SIMPLE DELETE: Snapshot items = \(deleteSnapshot)")
        
        // Use snapshot instead of current selectedItems to prevent any state pollution
        Task {
            await pantryService.deleteIngredientsSimple(withIDs: deleteSnapshot)
            
            // Clear selection and snapshot after deletion
            await MainActor.run {
                selectedItems.removeAll()
                deleteSnapshot.removeAll()
                deleteSnapshotNames = ""
                showDeleteConfirmation = false
                print("✅ SIMPLE DELETE: Completed successfully using snapshot")
            }
        }
    }

    // MARK: - Edit Individual Item (disabled)
    func startEditingIngredient(_ ingredient: Ingredient) { }
    func saveEditedIngredient() { }
    func cancelEditIngredient() {
        editingIngredient = nil
        editIngredientName = ""
        editIngredientCategory = .other
        showingEditIngredient = false
    }
    
    // 🔥 REMOVED: Category selection methods to prevent accidental mass selection
    // These methods were causing users to accidentally select entire categories
    // when they only wanted to select individual items

    // MARK: - Pantry Organization

    func organizePantry() async {
        // Reset state
        await MainActor.run {
            isOrganizing = true
            organizerSummary = nil
            organizerError = nil
        }

        do {
            // Create organizer service if needed
            if organizerService == nil {
                organizerService = PantryOrganizerService(
                    geminiService: GeminiAPIService(),
                    pantryService: pantryService
                )
            }

            guard let organizer = organizerService else {
                throw NSError(domain: "PantryViewModel", code: 1, userInfo: [NSLocalizedDescriptionKey: "Failed to create organizer service"])
            }

            // Get all pantry items
            let items = pantryService.pantryItems

            // Get allowed categories
            let allowedCategories = PantryCategory.allCases.map { $0.rawValue }

            // Generate cleanup plan
            let plan = try await organizer.organize(items: items, allowedCategories: allowedCategories)

            // Apply plan
            let summary = try await organizer.apply(plan: plan)

            // Update UI with summary
            await MainActor.run {
                isOrganizing = false
                organizerSummary = summary
                // The pantryItems are already updated by the organizer service
                // No need to reload since pantryService.pantryItems is @Published
            }
        } catch {
            await MainActor.run {
                isOrganizing = false
                organizerError = error
            }
        }
    }

    func dismissSummary() {
        organizerSummary = nil
    }

    func retryOrganization() async {
        organizerError = nil
        await organizePantry()
    }
}