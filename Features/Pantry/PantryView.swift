import SwiftUI

struct PantryView: View {
    @Environment(PantryService.self) var pantryService: PantryService
    
    var body: some View {
        PantryContentView(pantryService: pantryService)
    }
}

struct PantryContentView: View {
    @State(initialValue: nil) private var viewModel: PantryViewModel?
    let pantryService: PantryService

    init(pantryService: PantryService) {
        self.pantryService = pantryService
    }

    // Computed search suggestions based on ingredient library only
    private var searchSuggestions: [String] {
        IngredientLibrary.shared.allIngredientNames
    }

    var body: some View {
        Group {
            if let viewModel = viewModel {
                VStack {
                    List {
                        if pantryService.pantryItems.isEmpty {
                            emptyPantryView
                        } else {
                            // Recently Added Section with Grid layout
                            if !viewModel.recentlyAddedItems.isEmpty && !viewModel.isEditMode {
                                Section(header:
                                    HStack {
                                        Image(systemName: "star.fill")
                                            .foregroundColor(.orange)
                                            .font(.caption)
                                        Text("Recently Added")
                                            .font(.headline)
                                    }
                                ) {
                                    // Use LazyVGrid for better performance with many items
                                    LazyVGrid(columns: [
                                        GridItem(.flexible()),
                                        GridItem(.flexible())
                                    ], spacing: 12) {
                                        ForEach(viewModel.recentlyAddedItems) { ingredient in
                                            ingredientGridCell(ingredient: ingredient, isRecentlyAdded: true)
                                        }
                                    }
                                    .contentMargins(.all, 8)
                                }
                            }

                            // Categorized Items with improved layout
                            ForEach(viewModel.categorizedPantryItems, id: \.category) { categoryGroup in
                                Section(header: categoryHeader(for: categoryGroup.category, count: categoryGroup.ingredients.count)) {
                                    // Use Grid for better ingredient organization
                                    LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())], spacing: 12) {
                                        ForEach(categoryGroup.ingredients) { ingredient in
                                            ingredientGridCell(ingredient: ingredient, isRecentlyAdded: false)
                                                .overlay(
                                                    // 显示选中态轮廓，便于单个勾选删除
                                                    RoundedRectangle(cornerRadius: 10)
                                                        .stroke(
                                                            (viewModel.isEditMode && viewModel.selectedItems.contains(ingredient.id)) ? Color.accentColor : Color.clear,
                                                            lineWidth: 2
                                                        )
                                                )
                                        }
                                    }
                                    .contentMargins(.all, 8)
                                }
                            }
                        }

                        // Bulk Action Controls (shown in edit mode)
                        if viewModel.isEditMode && !pantryService.pantryItems.isEmpty {
                            Section {
                                bulkActionControls(viewModel: viewModel)
                            }
                        }
                    }
                    .listStyle(InsetGroupedListStyle())
                }
                .navigationTitle("My Pantry")
                .navigationBarTitleDisplayMode(.large)
                .toolbar {
                    ToolbarItem(placement: .navigationBarLeading) {
                        if viewModel.isEditMode {
                            Button("Cancel") {
                                viewModel.toggleEditMode()
                            }
                            .sensoryFeedback(.selection, trigger: viewModel.isEditMode)
                        }
                        Menu {
                            Picker("Sort", selection: Binding(
                                get: { viewModel.sortMode },
                                set: { viewModel.sortMode = $0 }
                            )) {
                                ForEach(PantryViewModel.SortMode.allCases) { mode in
                                    Text(mode.rawValue).tag(mode)
                                }
                            }
                        } label: {
                            Label("Sort", systemImage: "arrow.up.arrow.down")
                        }
                    }
                    
                    ToolbarItem(placement: .navigationBarTrailing) {
                        HStack {
                            if !pantryService.pantryItems.isEmpty {
                                Button(viewModel.isEditMode ? "Done" : "Edit") {
                                    viewModel.toggleEditMode()
                                }
                                .sensoryFeedback(.selection, trigger: viewModel.isEditMode)
                            }

                            Button(action: {
                                viewModel.showingAddIngredient = true
                            }) {
                                Image(systemName: "plus")
                                    .font(.title2)
                            }
                            .sensoryFeedback(.impact, trigger: viewModel.showingAddIngredient)
                        }
                    }
                }
                .sheet(isPresented: Binding(
                    get: { viewModel.showingAddIngredient },
                    set: { viewModel.showingAddIngredient = $0 }
                )) {
                    AddToPantryView(viewModel: viewModel)
                }
                // Remove edit-ingredient sheet per request
            } else {
                ProgressView("Loading pantry...")
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
        }
        .task {
            if viewModel == nil {
                viewModel = PantryViewModel(pantryService: pantryService)
            }
        }
    }
    
    // MARK: - Helper Views

    private var emptyPantryView: some View {
        VStack(spacing: 16) {
            Image(systemName: "cabinet")
                .font(.system(size: 60))
                .foregroundColor(.gray.opacity(0.5))

            Text("Your pantry is empty")
                .font(.title2.weight(.medium))
                .foregroundColor(.secondary)

            Text("Start by scanning ingredients or add them manually")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)

            Button(action: {
                viewModel?.showingAddIngredient = true
            }) {
                HStack {
                    Image(systemName: "plus.circle.fill")
                    Text("Add First Ingredient")
                }
                .font(.headline)
                .contentMargins(.horizontal, 20)
                .contentMargins(.vertical, 12)
                .background(.tint, in: RoundedRectangle(cornerRadius: 10))
                .foregroundColor(.white)
            }
            .sensoryFeedback(.impact, trigger: viewModel?.showingAddIngredient ?? false)
        }
        .contentMargins(.all, 40)
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
    }

    // Modern Grid Cell for ingredients
    private func ingredientGridCell(ingredient: Ingredient, isRecentlyAdded: Bool) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(ingredient.category.icon)
                    .font(.title2)
                
                VStack(alignment: .leading, spacing: 2) {
                    VStack(alignment: .leading, spacing: 0) {
                        Text(ingredient.name)
                        Text(ingredient.dateAdded, format: Date.FormatStyle().month().day().year())
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                        .font(.body.weight(.medium))
                        .lineLimit(1)
                    
                    if isRecentlyAdded {
                        Text("Recently Added")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                }
                
                Spacer()
                
                // remove trailing accessory
            }
        }
        .contentMargins(.all, 12)
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 10))
        .onTapGesture {
            if viewModel?.isEditMode == true {
                viewModel?.toggleItemSelection(ingredient.id)
            }
        }
        .sensoryFeedback(.selection, trigger: viewModel?.selectedItems.contains(ingredient.id) ?? false)
    }

    private func categoryHeader(for category: PantryCategory, count: Int) -> some View {
        HStack {
            HStack(spacing: 8) {
                Text(category.icon)
                    .font(.title3)
                
                Text(category.rawValue)
                    .font(.headline)
                
                Text("(\(count))")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 🔥 REMOVED: Category selection checkbox to prevent accidental mass selection
            // This was causing users to accidentally select entire categories when they only wanted individual items
        }
    }

    private func bulkActionControls(viewModel: PantryViewModel) -> some View {
        HStack {
            // 🔥 REMOVED: "Select All" button to prevent accidental clicks
            // Users were accidentally clicking this instead of "Delete Selected"
            
            Spacer()
            
            // 🔥 COMPLETE REWRITE: Simple, static delete button with snapshot
            Button("Delete Selected") {
                // 🔍 DETAILED DEBUG: Track state when delete button is clicked
                print("🔍 DEBUG: Delete Selected button clicked")
                print("🔍 DEBUG: selectedItems.count = \(viewModel.selectedItems.count)")
                print("🔍 DEBUG: selectedItems = \(viewModel.selectedItems)")
                
                // 🔥 Use snapshot mechanism to prevent state pollution
                viewModel.prepareDeletion()
                
                print("🔍 DEBUG: prepareDeletion called")
            }
            .disabled(viewModel.selectedItems.isEmpty)
            .foregroundColor(.red)
            .alert("Delete Items", isPresented: Binding(
                get: { viewModel.showDeleteConfirmation },
                set: { viewModel.showDeleteConfirmation = $0 }
            )) {
                Button("Delete", role: .destructive) {
                    viewModel.deleteSelectedItemsNow()
                }
                Button("Cancel", role: .cancel) { }
            } message: {
                // 🔥 SNAPSHOT MECHANISM: Use captured snapshot data to prevent state pollution
                Text("Delete \(viewModel.getDeleteSnapshotCount()) item\(viewModel.getDeleteSnapshotCount() == 1 ? "" : "s")?\n\n\(viewModel.getDeleteSnapshotNames())")
            }
        }
        .padding(.horizontal)
    }

    private func deleteItems(from category: PantryCategory, at offsets: IndexSet) {
        guard let viewModel = viewModel else { return }
        
        // Get the ingredients for this category
        let ingredientsInCategory = viewModel.categorizedPantryItems
            .first(where: { $0.category == category })?.ingredients ?? []

        // Delete each ingredient at the given offsets
        for offset in offsets {
            if offset < ingredientsInCategory.count {
                let ingredient = ingredientsInCategory[offset]
                viewModel.deleteIngredient(ingredient, from: category)
            }
        }
    }
}

// MARK: - Add to Pantry View

struct AddToPantryView: View {
    @Bindable var viewModel: PantryViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var selectedCategory: PantryCategory? = nil
    @State private var selectedNames: Set<String> = []
    @State private var searchText: String = ""
    @State private var expandedCategories: Set<PantryCategory> = []

    private var categories: [PantryCategory] {
        [
            .produce, .proteins, .dairy, .plantBasedAlternatives, .bakery,
            .grainsPastaLegumes, .spicesAndSeasonings, .oilsVinegarsAndCondiments,
            .cannedAndBroths, .nutsAndSeeds, .snacks, .bakingAndSweeteners, .other
        ]
    }

    private func items(for category: PantryCategory) -> [String] {
        IngredientLibrary.shared.items(for: category).filter { item in
            searchText.isEmpty || item.lowercased().hasPrefix(searchText.lowercased())
        }
    }

    var body: some View {
        NavigationStack {
            content
                .navigationTitle("Add Ingredients")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarLeading) {
                        Button("Cancel") { dismiss() }
                    }
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("Save") {
                            // No normalization - use ingredient names as-is
                            let grouped = IngredientLibrary.shared.group(names: Array(selectedNames))
                            let items: [Ingredient] = grouped.map { (name, category) in
                                return Ingredient(name: name, category: category, dateAdded: Date())
                            }
                            viewModel.addIngredients(items)
                            dismiss()
                        }
                        .disabled(selectedNames.isEmpty)
                    }
                }
                .navigationDestination(for: PantryCategory.self) { category in
                    CategoryIngredientsView(category: category, selectedNames: $selectedNames)
                }
        }
    }

    @ViewBuilder
    private var content: some View {
        VStack(spacing: 12) {
            // 搜索结果模式：当用户输入前缀时，仅显示匹配的库内食材列表
            HStack {
                TextField("Type prefix (e.g., 'avo')", text: $searchText)
                    .textFieldStyle(.roundedBorder)
            }
            .padding(.horizontal)

            if !searchText.trimmingCharacters(in: .whitespaces).isEmpty {
                let results = IngredientLibrary.shared.suggest(prefix: searchText.lowercased(), limit: 200)
                ScrollView {
                    LazyVStack(alignment: .leading, spacing: 8) {
                        if results.isEmpty {
                            Text("No results in library for \"\(searchText)\"")
                                .foregroundColor(.secondary)
                                .padding(.horizontal)
                                .padding(.top, 8)
                        } else {
                            ForEach(results, id: \.self) { name in
                                HStack {
                                    let isSelected = selectedNames.contains(name)
                                    Button(action: {
                                        if isSelected { selectedNames.remove(name) } else { selectedNames.insert(name) }
                                    }) {
                                        HStack(spacing: 8) {
                                            Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                                                .foregroundColor(isSelected ? .green : .secondary)
                                            Text(name)
                                                .font(.body)
                                                .lineLimit(1)
                                        }
                                        .padding(.vertical, 6)
                                        .padding(.horizontal, 10)
                                        .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 8))
                                    }
                                    .buttonStyle(.plain)
                                    Spacer()
                                }
                                .padding(.horizontal)
                            }
                        }
                    }
                    .padding(.top, 4)
                }
            } else {
                // 分类模式：展示大类，点击进入分类详情页
                categoryListNavigator
            }
        }
    }

    @ViewBuilder
    private var categoryListNavigator: some View {
        List {
            ForEach(categories, id: \.self) { category in
                NavigationLink(value: category) {
                    HStack {
                        Text(category.icon)
                        Text(category.rawValue)
                            .font(.headline)
                        Spacer()
                        if selectedCount(for: category) > 0 {
                            Text("\(selectedCount(for: category)) selected")
                                .font(.footnote)
                                .foregroundStyle(.tint)
                        }
                    }
                }
            }
        }
        .listStyle(.insetGrouped)
    }

    private var gridColumns: [GridItem] {
        Array(repeating: GridItem(.flexible(), spacing: 8), count: 3)
    }

    @ViewBuilder
    private func ingredientChip(_ name: String) -> some View {
        let isSelected = selectedNames.contains(name)
        Button(action: {
            if isSelected { selectedNames.remove(name) } else { selectedNames.insert(name) }
        }) {
            HStack(spacing: 6) {
                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(isSelected ? .green : .secondary)
                Text(name)
                    .lineLimit(1)
                    .font(.footnote)
            }
            .padding(8)
            .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 8))
        }
        .buttonStyle(.plain)
    }

    private func selectedCount(for category: PantryCategory) -> Int {
        let set = Set(IngredientLibrary.shared.items(for: category))
        return selectedNames.filter { set.contains($0) }.count
    }
}

// MARK: - Edit Ingredient View

struct EditIngredientView: View {
    @Bindable var viewModel: PantryViewModel
    @Environment(\.dismiss) private var dismiss
    @FocusState private var isFocused: Bool

    var body: some View {
            Form {
                Section(header: Text("Edit Ingredient")) {
                    TextField("Ingredient name", text: $viewModel.editIngredientName)
                        .focused($isFocused)
                        .onSubmit {
                            viewModel.saveEditedIngredient()
                            dismiss()
                        }
                }

                Section(header: Text("Category")) {
                    Picker("Category", selection: $viewModel.editIngredientCategory) {
                        ForEach(PantryCategory.allCases, id: \.self) { category in
                            HStack {
                                Text(category.icon)
                                Text(category.rawValue)
                            }
                            .tag(category)
                        }
                    }
                    .pickerStyle(WheelPickerStyle())
                }
            }
            .navigationTitle("Edit Ingredient")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("Cancel") {
                    viewModel.cancelEditIngredient()
                    dismiss()
                },
                trailing: Button("Save") {
                    viewModel.saveEditedIngredient()
                    dismiss()
                }
                .disabled(viewModel.editIngredientName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
            )
        .onAppear {
            isFocused = true
        }
    }
}

// MARK: - Category Ingredients View (Detail)

struct CategoryIngredientsView: View {
    let category: PantryCategory
    @Binding var selectedNames: Set<String>
    @State private var searchText: String = ""

    private var names: [String] {
        let base = IngredientLibrary.shared.items(for: category)
        let q = searchText.trimmingCharacters(in: .whitespacesAndNewlines)
        if q.isEmpty { return base }
        let lowerQ = q.lowercased()
        return base.filter { $0.lowercased().hasPrefix(lowerQ) }
    }

    var body: some View {
        ScrollView {
            if names.isEmpty {
                Text("No items")
                    .foregroundColor(.secondary)
                    .padding()
            } else {
                LazyVGrid(columns: columns, spacing: 10) {
                    ForEach(names, id: \.self) { name in
                        chip(name)
                    }
                }
                .padding(.horizontal)
                .padding(.top, 8)
            }
        }
        .searchable(text: $searchText, placement: .navigationBarDrawer(displayMode: .always), prompt: Text("Filter in \(category.rawValue)"))
        .navigationTitle(category.rawValue)
        .navigationBarTitleDisplayMode(.inline)
    }

    private func toggle(_ name: String) {
        if selectedNames.contains(name) {
            selectedNames.remove(name)
        } else {
            selectedNames.insert(name)
        }
    }

    @ViewBuilder
    private func chip(_ name: String) -> some View {
        let isSelected = selectedNames.contains(name)
        Button(action: { toggle(name) }) {
            Text(name)
                .font(.footnote.weight(.medium))
                .lineLimit(nil)
                .multilineTextAlignment(.center)
                .fixedSize(horizontal: false, vertical: true) // 允许多行，避免溢出重叠
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .foregroundColor(isSelected ? .white : .primary)
                .background(
                    RoundedRectangle(cornerRadius: 10, style: .continuous)
                        .fill(isSelected ? Color.accentColor : Color(uiColor: .systemGray6))
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 10, style: .continuous)
                        .stroke(isSelected ? Color.accentColor : Color.clear, lineWidth: 0)
                )
                .animation(.easeInOut(duration: 0.12), value: isSelected)
        }
        .buttonStyle(.plain)
    }

    private var columns: [GridItem] {
        // 自适应列，保证每个泡泡最小宽度，自动换行不重叠
        [GridItem(.adaptive(minimum: 120), spacing: 10)]
    }
}