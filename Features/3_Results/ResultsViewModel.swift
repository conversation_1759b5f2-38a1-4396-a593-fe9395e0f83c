import SwiftUI

@Observable
@MainActor
class ResultsViewModel {
    private let pantryService: PantryService
    private let navigationCoordinator: NavigationCoordinator
    
    var ingredients: [Ingredient]
    var selectedIngredientIds: Set<UUID> = []
    var editingIngredient: Ingredient? = nil
    
    // Legacy properties for backward compatibility
    var showingNoIngredientsAlert = false
    var hasAddedToPantry = false
    var showingSuccessState = false
    var addedItemsCount = 0
    var showingEditIngredient = false
    var editIngredientName = ""
    var editIngredientCategory: PantryCategory = .other
    
    // Additional data loading properties
    var isLoadingAdditionalData = false
    var additionalDataLoaded = false
    var visionResponse: String?
    var geminiResponse: String?
    var isDebugModeEnabled: Bool {
        #if DEBUG
        return true
        #else
        return false
        #endif
    }
    
    // Enhanced ingredient data
    var enhancedIngredients: [EnhancedIngredient] = []
    
    // Get ingredients grouped by category - updated to use dictionary format as specified in task
    var ingredientsByCategory: [PantryCategory: [Ingredient]] {
        Dictionary(grouping: ingredients) { $0.category }
    }
    
    // Legacy computed property for backward compatibility
    var categorizedIngredients: [(category: PantryCategory, ingredients: [Ingredient])] {
        let grouped = Dictionary(grouping: ingredients) { $0.category }
        
        // Sort categories in a logical order
        let categoryOrder: [PantryCategory] = [
            .produce,
            .proteins,
            .dairy,
            .plantBasedAlternatives,
            .bakery,
            .grainsPastaLegumes,
            .spicesAndSeasonings,
            .oilsVinegarsAndCondiments,
            .cannedAndBroths,
            .nutsAndSeeds,
            .snacks,
            .bakingAndSweeteners,
            .other
        ]
        
        return categoryOrder.compactMap { category in
            guard let items = grouped[category], !items.isEmpty else { return nil }
            return (category: category, ingredients: items)
        }
    }
    
    init(ingredients: [Ingredient], pantryService: PantryService, navigationCoordinator: NavigationCoordinator) {
        self.ingredients = ingredients
        self.pantryService = pantryService
        self.navigationCoordinator = navigationCoordinator
        
        // Select all ingredients by default as specified in task
        self.selectedIngredientIds = Set(ingredients.map { $0.id })
        
        // Check if no ingredients were found
        if ingredients.isEmpty {
            showingNoIngredientsAlert = true
        }
    }
    
    // Legacy init for backward compatibility
    init(ingredients: [Ingredient], pantryService: PantryService) {
        self.ingredients = ingredients
        self.pantryService = pantryService
        // Note: navigationCoordinator will be set by the view when available
        self.navigationCoordinator = NavigationCoordinator()
        
        // Select all ingredients by default
        self.selectedIngredientIds = Set(ingredients.map { $0.id })
        
        // Check if no ingredients were found
        if ingredients.isEmpty {
            showingNoIngredientsAlert = true
        }
    }
    
    // MARK: - New Selection Methods for Task 33
    
    /// Toggle selection for an ingredient
    func toggleSelection(for ingredient: Ingredient) {
        if selectedIngredientIds.contains(ingredient.id) {
            selectedIngredientIds.remove(ingredient.id)
        } else {
            selectedIngredientIds.insert(ingredient.id)
        }
    }
    
    /// Start editing an ingredient
    func startEditing(ingredient: Ingredient) {
        editingIngredient = ingredient
        editIngredientName = ingredient.name
        editIngredientCategory = ingredient.category
        showingEditIngredient = true
    }
    
    /// Update edited ingredient
    func updateIngredient(id: UUID, name: String, category: PantryCategory) {
        guard let index = ingredients.firstIndex(where: { $0.id == id }) else { return }
        ingredients[index] = Ingredient(id: id, name: name, category: category)
        editingIngredient = nil
        showingEditIngredient = false
    }
    
    /// Add selected ingredients to pantry
    func addSelectedToPantry() async {
        let selectedIngredients = ingredients.filter { selectedIngredientIds.contains($0.id) }
        // Note: PantryService.addIngredients doesn't throw, it handles errors internally
        await pantryService.addIngredients(selectedIngredients)
        // Navigate to Pantry tab
        navigationCoordinator.switchToPantryTab()
        // Reset scan flow
        navigationCoordinator.resetScanFlow()
    }
    
    /// Restart scanning
    func restartScanning() {
        navigationCoordinator.resetScanFlow()
    }
    
    /// Load additional data for ingredients such as nutritional info, storage tips, etc.
    /// This method can be called by the view using .task(id:) for reactive data loading
    func loadAdditionalData() async throws {
        isLoadingAdditionalData = true
        additionalDataLoaded = false
        
        do {
            // Check for cancellation before starting
            if Task.isCancelled {
                throw TaskCancellationError()
            }
            
            // Simulate loading additional data (nutritional info, storage tips, etc.)
            try await Task.sleep(for: .milliseconds(500))
            
            // Check for cancellation after delay
            if Task.isCancelled {
                throw TaskCancellationError()
            }
            
            // Process ingredients to add additional data
            enhancedIngredients = ingredients.map { ingredient in
                EnhancedIngredient(
                    ingredient: ingredient,
                    storageTips: generateStorageTips(for: ingredient),
                    shelfLife: generateShelfLife(for: ingredient),
                    nutritionalHighlights: generateNutritionalHighlights(for: ingredient)
                )
            }
            
            additionalDataLoaded = true
            isLoadingAdditionalData = false
            
        } catch {
            isLoadingAdditionalData = false
            if Task.isCancelled {
                throw TaskCancellationError()
            } else {
                throw error
            }
        }
    }
    
    // MARK: - Legacy Methods for Backward Compatibility
    
    func toggleIngredient(at index: Int) {
        ingredients[index].isSelected.toggle()
    }
    
    func toggleIngredient(_ ingredient: Ingredient) {
        if let index = ingredients.firstIndex(where: { $0.id == ingredient.id }) {
            ingredients[index].isSelected.toggle()
        }
    }
    
    func getSelectedIngredients() -> [Ingredient] {
        ingredients.filter { $0.isSelected }
    }
    
    func addToPantry() async -> Bool {
        let selectedIngredients = getSelectedIngredients()
        if !selectedIngredients.isEmpty {
            addedItemsCount = selectedIngredients.count
            await pantryService.addIngredients(selectedIngredients)
            hasAddedToPantry = true

            // Store recently added items for highlighting
            pantryService.markAsRecentlyAdded(selectedIngredients)

            // Show success state with animation
            withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                showingSuccessState = true
            }
            
            return true
        }
        return false
    }

    // MARK: - Smart Selection Methods

    func selectAll() {
        selectedIngredientIds = Set(ingredients.map { $0.id })
        // Also update legacy selection for backward compatibility
        for i in ingredients.indices {
            ingredients[i].isSelected = true
        }
    }

    func deselectAll() {
        selectedIngredientIds.removeAll()
        // Also update legacy selection for backward compatibility
        for i in ingredients.indices {
            ingredients[i].isSelected = false
        }
    }

    func toggleCategorySelection(_ category: PantryCategory) {
        let categoryIngredients = ingredients.filter { $0.category == category }
        let allSelected = categoryIngredients.allSatisfy { selectedIngredientIds.contains($0.id) }

        for ingredient in categoryIngredients {
            if allSelected {
                selectedIngredientIds.remove(ingredient.id)
            } else {
                selectedIngredientIds.insert(ingredient.id)
            }
        }
        
        // Also update legacy selection for backward compatibility
        for i in ingredients.indices {
            if ingredients[i].category == category {
                ingredients[i].isSelected = !allSelected
            }
        }
    }

    func isCategoryFullySelected(_ category: PantryCategory) -> Bool {
        let categoryIngredients = ingredients.filter { $0.category == category }
        return !categoryIngredients.isEmpty && categoryIngredients.allSatisfy { selectedIngredientIds.contains($0.id) }
    }

    // MARK: - Edit Individual Ingredient

    func startEditingIngredient(_ ingredient: Ingredient) {
        startEditing(ingredient: ingredient)
    }

    func saveEditedIngredient() {
        guard let ingredient = editingIngredient else { return }

        let trimmedName = editIngredientName.trimmingCharacters(in: .whitespacesAndNewlines)
        if !trimmedName.isEmpty {
            updateIngredient(id: ingredient.id, name: trimmedName, category: editIngredientCategory)
        }

        cancelEditIngredient()
    }

    func cancelEditIngredient() {
        editingIngredient = nil
        editIngredientName = ""
        editIngredientCategory = .other
        showingEditIngredient = false
    }
    
    // MARK: - Additional Data Generation Helpers
    
    private func generateStorageTips(for ingredient: Ingredient) -> String {
        switch ingredient.category {
        case .produce:
            return "Store in refrigerator crisper drawer"
        case .proteins:
            return "Keep refrigerated, use within 2-3 days"
        case .dairy, .plantBasedAlternatives:
            return "Refrigerate and check expiration date"
        case .grainsPastaLegumes:
            return "Store in cool, dry place in airtight container"
        default:
            return "Follow package instructions for storage"
        }
    }
    
    private func generateShelfLife(for ingredient: Ingredient) -> String {
        switch ingredient.category {
        case .produce:
            return "3-7 days"
        case .proteins:
            return "2-3 days"
        case .dairy, .plantBasedAlternatives:
            return "5-10 days"
        case .grainsPastaLegumes:
            return "6-12 months"
        case .cannedAndBroths:
            return "Check expiration date"
        default:
            return "Varies"
        }
    }
    
    private func generateNutritionalHighlights(for ingredient: Ingredient) -> [String] {
        switch ingredient.category {
        case .produce:
            return ["High in vitamins", "Rich in fiber", "Low calories"]
        case .proteins:
            return ["High protein", "Essential amino acids", "Iron source"]
        case .dairy, .plantBasedAlternatives:
            return ["Calcium rich", "Protein source", "Vitamin D"]
        case .grainsPastaLegumes:
            return ["Complex carbohydrates", "B vitamins", "Energy source"]
        default:
            return ["Nutritional content varies"]
        }
    }
}

// MARK: - Enhanced Ingredient Model

struct EnhancedIngredient {
    let ingredient: Ingredient
    let storageTips: String
    let shelfLife: String
    let nutritionalHighlights: [String]
}

// MARK: - Custom Error Types are now in Models/Ingredient.swift 