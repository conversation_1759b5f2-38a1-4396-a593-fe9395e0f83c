import SwiftUI
import UIKit
import PhotosUI

// MARK: - Processing Error Types

enum ScanError: Error, LocalizedError {
    case visionError(String)
    case geminiError(String)
    case partialVisionSuccess(successCount: Int, totalCount: Int)
    case networkError(String)
    case timeout
    case noImages
    case navigationFailed

    var errorDescription: String? {
        switch self {
        case .visionError(let message):
            return "Image analysis error: \(message)"
        case .geminiError(let message):
            return "Text processing error: \(message)"
        case .partialVisionSuccess(let successCount, let totalCount):
            return "Processed \(successCount) of \(totalCount) images successfully"
        case .networkError(let message):
            return "Network error: \(message)"
        case .timeout:
            return "Processing timed out. Please try again with fewer images."
        case .noImages:
            return "No images selected for processing."
        case .navigationFailed:
            return "Navigation to results failed."
        }
    }

    var recoverySuggestion: String? {
        switch self {
        case .visionError, .geminiError, .networkError:
            return "Please try again or use fewer images"
        case .partialVisionSuccess:
            return "Some images couldn't be processed. You can continue with partial results or try again"
        case .timeout:
            return "Try processing fewer images at once or check your internet connection."
        case .noImages:
            return "Add at least one image to process."
        case .navigationFailed:
            return "Please restart the app and try again."
        }
    }

    var canRetry: Bool {
        switch self {
        case .visionError, .geminiError, .networkError, .timeout:
            return true
        case .partialVisionSuccess:
            return true // Allow retry to try processing failed images again
        case .noImages, .navigationFailed:
            return false
        }
    }

    var allowsContinue: Bool {
        switch self {
        case .partialVisionSuccess:
            return true // Allow continuing with partial results
        default:
            return false
        }
    }
}

// Keep ProcessingError for backward compatibility
typealias ProcessingError = ScanError

@Observable
@MainActor
class StagingViewModel {
    private weak var navigationCoordinator: NavigationCoordinator?
    
    // MARK: - Service Dependencies
    
    private let visionService: GoogleVisionAPIService
    private let geminiService: GeminiAPIService
    
    // MARK: - State Properties
    
    var selectedImages: [UIImage] = []
    var showingImagePicker = false
    var showingCamera = false
    var showingEnhancedCamera = false
    var isProcessing = false
    var isLoadingImages = false
    var selectedImage: UIImage?
    
    // MARK: - Enhanced Processing Flow Properties
    
    /// Processing error for the new direct flow
    var processingError: Error?

    /// Processing progress for overlay (0.0 to 1.0)
    var processingProgress: Double = 0.0

    /// Partial results from processing (for partial success scenarios)
    var partialResults: [Ingredient] = []
    
    /// Maximum number of images allowed (increased from 3 to 10 as per PRD)
    private let maxImages = 10
    
    // MARK: - Computed Properties
    
    var canAddMoreImages: Bool {
        selectedImages.count < maxImages
    }
    
    var remainingSlots: Int {
        maxImages - selectedImages.count
    }
    
    /// Check if Process button should be shown (≥1 image present)
    var canProcess: Bool {
        return !selectedImages.isEmpty && !isProcessing
    }
    
    var processButtonText: String {
        let count = selectedImages.count
        if count == 0 {
            return "Add Images to Process"
        } else if count == 1 {
            return "Process 1 Image"
        } else {
            return "Process \(count) Images"
        }
    }
    
    init(navigationCoordinator: NavigationCoordinator, 
         visionService: GoogleVisionAPIService = ServiceContainer.shared.googleVisionService,
         geminiService: GeminiAPIService = ServiceContainer.shared.geminiService) {
        self.navigationCoordinator = navigationCoordinator
        self.visionService = visionService
        self.geminiService = geminiService
    }
    
    // MARK: - Image Management
    
    func addImage(_ image: UIImage) {
        guard canAddMoreImages else { return }
        selectedImages.append(image)
    }
    
    func addImages(_ images: [UIImage]) {
        let availableSlots = remainingSlots
        let imagesToAdd = Array(images.prefix(availableSlots))
        selectedImages.append(contentsOf: imagesToAdd)
    }
    
    func removeImage(at index: Int) {
        guard index >= 0 && index < selectedImages.count else { return }
        selectedImages.remove(at: index)
    }
    
    /// Clear all images - useful for restarting the scan flow
    func clearImages() {
        selectedImages.removeAll()
        processingError = nil
        processingProgress = 0.0
    }
    
    // MARK: - Primary Button Actions
    
    /// Scan Receipts and Ingredients - Opens enhanced camera for scanning
    func scanReceiptsAndIngredients() {
        guard canAddMoreImages else { return }
        
        // Use enhanced camera for better scanning experience
        showingEnhancedCamera = true
    }
    
    func chooseFromLibrary() {
        // PHPickerViewController doesn't require permissions - show directly
        showingImagePicker = true
    }
    
    // MARK: - Legacy Camera Support
    
    func takePhoto() {
        guard canAddMoreImages else { return }

        // Check if camera is available before showing
        guard UIImagePickerController.isSourceTypeAvailable(.camera) else {
            print("⚠️ Camera not available on this device")
            return
        }

        // UIImagePickerController handles camera permissions automatically
        showingCamera = true
    }
    
    // MARK: - Processing Flow
    
    /// Process images using the new direct Vision → Gemini pipeline with enhanced error handling
    /// This is the main processing method called by the Process button
    func processImages() async {
        guard !selectedImages.isEmpty else {
            processingError = ScanError.noImages
            return
        }

        // Show processing overlay (ensure on main thread)
        await MainActor.run {
            isProcessing = true
            processingError = nil
            processingProgress = 0.0
            partialResults = []
        }

        do {
            // Add timeout mechanism to prevent indefinite loading
            let timeoutTask = Task {
                try await Task.sleep(nanoseconds: 120_000_000_000) // 120 seconds timeout
                throw ScanError.timeout
            }

            let processingTask = Task {
                do {
                    // Step 1: Process images with Vision API individually to handle partial failures
                    await MainActor.run { processingProgress = 0.1 }

                    var visionOutputs: [GoogleVisionAPIService.VisionOutput] = []
                    var failedImageCount = 0

                    // Try to process each image individually to handle partial failures
                    for (index, image) in selectedImages.enumerated() {
                        do {
                            let outputs = try await visionService.batchAnalyze(images: [image])
                            if !outputs.isEmpty {
                                visionOutputs.append(contentsOf: outputs)
                            } else {
                                failedImageCount += 1
                            }
                        } catch {
                            failedImageCount += 1
                            // Continue with other images
                            print("Failed to process image \(index + 1): \(error.localizedDescription)")
                        }

                        // Update progress for each image processed
                        await MainActor.run {
                            processingProgress = 0.1 + (0.5 * Double(index + 1) / Double(selectedImages.count))
                        }
                    }

                    // Check if we have at least some results
                    if visionOutputs.isEmpty {
                        throw ScanError.visionError("Could not process any images")
                    }

                    await MainActor.run { processingProgress = 0.6 }

                    // Step 2: Process Vision outputs with Gemini
                    let allowedCategories = PantryCategory.allCases.map { $0.rawValue }
                    let ingredients = try await geminiService.canonicalizeIngredients(
                        visionOutputs: visionOutputs,
                        allowedCategories: allowedCategories
                    )
                    await MainActor.run { processingProgress = 0.9 }

                    // If we had partial success, note it but still return results
                    if failedImageCount > 0 {
                        await MainActor.run {
                            partialResults = ingredients
                        }
                        throw ScanError.partialVisionSuccess(
                            successCount: selectedImages.count - failedImageCount,
                            totalCount: selectedImages.count
                        )
                    }

                    return ingredients
                } catch let error as ScanError {
                    throw error
                } catch {
                    // Wrap errors with context
                    if error is GeminiError {
                        throw ScanError.geminiError(error.localizedDescription)
                    } else if let urlError = error as? URLError {
                        throw ScanError.networkError(urlError.localizedDescription)
                    } else if (error as NSError).domain == NSURLErrorDomain {
                        throw ScanError.networkError(error.localizedDescription)
                    } else {
                        throw ScanError.visionError(error.localizedDescription)
                    }
                }
            }

            // Race between processing and timeout
            let ingredients = try await withThrowingTaskGroup(of: [Ingredient].self, returning: [Ingredient].self) { group in
                group.addTask { try await processingTask.value }
                group.addTask {
                    try await timeoutTask.value
                    return [] // This will never be reached due to timeout error
                }

                // Get the first completed task result
                let result = try await group.next()!
                group.cancelAll() // Cancel the remaining task
                return result
            }

            // Step 3: Navigate to Results (ensure on main thread)
            await MainActor.run {
                isProcessing = false
                processingProgress = 1.0
            }

            // Check if we have ingredients to display
            guard !ingredients.isEmpty else {
                await MainActor.run {
                    processingError = ScanError.geminiError("No ingredients could be identified from the images")
                }
                return
            }

            // Navigate to results
            guard let navigationCoordinator = navigationCoordinator else {
                await MainActor.run {
                    processingError = ScanError.navigationFailed
                }
                return
            }

            navigationCoordinator.navigateToResults(ingredients: ingredients)

        } catch let error as ScanError {
            // Handle ScanError with partial results support
            await MainActor.run {
                isProcessing = false
                processingError = error
                processingProgress = 0.0

                // For partial success, keep the partial results available
                if case .partialVisionSuccess = error {
                    // partialResults already set in the processing task
                }
            }
        } catch {
            // Handle other errors
            await MainActor.run {
                isProcessing = false
                processingError = ScanError.networkError(error.localizedDescription)
                processingProgress = 0.0
            }
        }
    }
    

    
    /// Restart the scanning process - clear all state
    func restartScanning() {
        selectedImages.removeAll()
        processingError = nil
        processingProgress = 0.0
        isProcessing = false
        partialResults = []
        // Reset any other UI state if needed
        showingImagePicker = false
        showingCamera = false
        showingEnhancedCamera = false
        selectedImage = nil
        isLoadingImages = false
    }

    /// Retry processing with the same images after an error
    func retryProcessing() async {
        guard !selectedImages.isEmpty else { return }
        await processImages()
    }

    /// Clear error state without clearing images
    func clearError() {
        processingError = nil
        partialResults = []
    }

    /// Continue with partial results (for partial success scenarios)
    func continueWithPartialResults() {
        guard !partialResults.isEmpty else { return }

        // Navigate to results with partial results
        guard let navigationCoordinator = navigationCoordinator else {
            processingError = ScanError.navigationFailed
            return
        }

        navigationCoordinator.navigateToResults(ingredients: partialResults)

        // Clear the error and partial results
        processingError = nil
        partialResults = []
    }
    
    // MARK: - Image Handling
    
    func handleCameraImage(_ image: UIImage?) {
        showingCamera = false
        if let image = image {
            addImage(image)
        }
        selectedImage = nil // Clear after use
    }
    
    @MainActor
    func handlePickerImages(_ images: [UIImage]) {
        showingImagePicker = false
        isLoadingImages = true
        
        // Process images immediately on main thread
        addImages(images)
        isLoadingImages = false
        // Note: We don't clear selectedImages here as that's our main storage
        // The picker's selectedImages parameter is handled by the picker itself
    }
} 