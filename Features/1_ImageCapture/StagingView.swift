import SwiftUI

struct StagingView: View {
    @State var viewModel: StagingViewModel

    var body: some View {
        ZStack {
            VStack(spacing: 20) {
                // Title with adaptive layout
                ViewThatFits(in: .horizontal) {
                    // Wide layout
                    HStack {
                        Text("Ingredient Scanner")
                            .font(.largeTitle.weight(.bold))
                        Spacer()
                        Text("\(viewModel.selectedImages.count)/10")
                            .font(.title2.weight(.medium))
                            .foregroundColor(.secondary)
                    }
                    
                    // Narrow layout (original)
                    VStack(spacing: 8) {
                        Text("Ingredient Scanner")
                            .font(.largeTitle.weight(.bold))
                        Text("Selected Images (\(viewModel.selectedImages.count)/10)")
                            .font(.headline)
                            .foregroundColor(.secondary)
                    }
                }
                .contentMargins(.top, 16)
                
                // Primary Buttons - Two main actions
                VStack(spacing: 16) {
                    // Scan Receipts and Ingredients Button
                    Button(action: { 
                        viewModel.scanReceiptsAndIngredients() 
                    }) {
                        HStack {
                            Image(systemName: "camera.fill")
                                .font(.title2)
                            Text("Scan Receipts and Ingredients")
                                .font(.headline)
                                .fontWeight(.semibold)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 18)
                        .background(
                            viewModel.canAddMoreImages && !viewModel.isProcessing ? 
                            Color.blue : Color.gray.opacity(0.5), 
                            in: RoundedRectangle(cornerRadius: 12)
                        )
                        .foregroundColor(.white)
                    }
                    .disabled(!viewModel.canAddMoreImages || viewModel.isProcessing)
                    .sensoryFeedback(.impact, trigger: viewModel.canAddMoreImages)
                    
                    // Choose from Library Button
                    Button(action: { 
                        viewModel.chooseFromLibrary() 
                    }) {
                        HStack {
                            Image(systemName: "photo.on.rectangle.angled")
                                .font(.title2)
                            Text("Choose from Library")
                                .font(.headline)
                                .fontWeight(.semibold)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 18)
                        .background(
                            viewModel.canAddMoreImages && !viewModel.isProcessing ? 
                            Color.green : Color.gray.opacity(0.5), 
                            in: RoundedRectangle(cornerRadius: 12)
                        )
                        .foregroundColor(.white)
                    }
                    .disabled(!viewModel.canAddMoreImages || viewModel.isProcessing)
                    .sensoryFeedback(.impact, trigger: viewModel.canAddMoreImages)
                }
                .contentMargins(.horizontal, 20)
                
                // Image Grid - Display selected images with delete functionality
                if viewModel.selectedImages.isEmpty {
                    // Empty state
                    VStack(spacing: 20) {
                        Image(systemName: "photo.on.rectangle.angled")
                            .font(.system(size: 60))
                            .foregroundColor(.gray.opacity(0.5))
                        
                        Text("Add up to 10 images to scan")
                            .font(.title3)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity)
                    .frame(height: 200)
                    .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
                    .contentMargins(.horizontal, 20)
                } else {
                    // Image grid with LazyVGrid for proper layout
                    ScrollView {
                        LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 3), spacing: 12) {
                            ForEach(Array(viewModel.selectedImages.enumerated()), id: \.offset) { index, image in
                                ZStack(alignment: .topTrailing) {
                                    Image(uiImage: image)
                                        .resizable()
                                        .scaledToFill()
                                        .frame(width: 100, height: 100)
                                        .clipShape(RoundedRectangle(cornerRadius: 10))
                                        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 10))
                                        .shadow(radius: 3)
                                    
                                    // Delete button overlay
                                    Button(action: {
                                        withAnimation(.spring()) {
                                            viewModel.removeImage(at: index)
                                        }
                                    }) {
                                        Image(systemName: "xmark.circle.fill")
                                            .font(.title3)
                                            .foregroundColor(.white)
                                            .background(Color.red, in: Circle())
                                            .clipShape(Circle())
                                            .shadow(radius: 2)
                                    }
                                    .sensoryFeedback(.impact(weight: .medium), trigger: viewModel.selectedImages.count)
                                    .offset(x: 8, y: -8)
                                }
                            }
                        }
                        .padding(.horizontal, 20)
                    }
                    .frame(maxHeight: 300)
                }
                
                // Process Button - Appears only when at least one image is selected
                if viewModel.canProcess {
                    Button(action: { 
                        Task {
                            await viewModel.processImages()
                        }
                    }) {
                        HStack {
                            Image(systemName: "wand.and.rays")
                                .font(.title2)
                            Text("Process Images")
                                .font(.headline)
                                .fontWeight(.semibold)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 18)
                        .background(Color.orange, in: RoundedRectangle(cornerRadius: 12))
                        .foregroundColor(.white)
                    }
                    .disabled(viewModel.isProcessing)
                    .sensoryFeedback(.impact(weight: .heavy), trigger: viewModel.canProcess)
                    .contentMargins(.horizontal, 20)
                }
                
                Spacer()
                
                // Help text with improved styling
                VStack(spacing: 8) {
                    Text("Tips for better results:")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                            Text("Ensure good lighting")
                                .font(.body)
                        }
                        
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                            Text("Keep ingredients clearly visible")
                                .font(.body)
                        }
                        
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                            Text("Avoid blurry or tilted photos")
                                .font(.body)
                        }
                    }
                }
                .contentMargins(.all, 16)
                .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
                .contentMargins(.horizontal, 20)
            }
            
            // Processing Overlay - Full-screen overlay when processing
            if viewModel.isProcessing {
                VStack(spacing: 20) {
                    ProgressView()
                        .scaleEffect(1.5)
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    
                    Text("Processing...")
                        .font(.headline)
                        .foregroundColor(.white)
                    
                    if viewModel.processingProgress > 0 {
                        ProgressView(value: viewModel.processingProgress)
                            .progressViewStyle(LinearProgressViewStyle(tint: .white))
                            .frame(width: 200)
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color.black.opacity(0.7))
                .edgesIgnoringSafeArea(.all)
            }
            
            // Error Overlay - Show processing errors with retry options
            if let error = viewModel.processingError {
                ErrorOverlayView(
                    error: error,
                    partialResultsCount: viewModel.partialResults.count,
                    onContinue: { viewModel.continueWithPartialResults() },
                    onRetry: { Task { await viewModel.retryProcessing() } },
                    onDismiss: { viewModel.clearError() }
                )
            }
        }
        .sheet(isPresented: $viewModel.showingCamera) {
            ImagePicker(selectedImage: $viewModel.selectedImage, sourceType: .camera)
        }
        .sheet(isPresented: $viewModel.showingImagePicker) {
            MultiImagePicker(selectedImages: $viewModel.selectedImages, maxSelection: 10 - viewModel.selectedImages.count)
        }
        .sheet(isPresented: $viewModel.showingEnhancedCamera) {
            EnhancedCameraView(
                onImagesSelected: { images in
                    viewModel.addImages(images)
                    viewModel.showingEnhancedCamera = false
                },
                maxImages: 10 - viewModel.selectedImages.count
            )
        }
        .accessibilityRotor("Images") {
            ForEach(Array(viewModel.selectedImages.indices), id: \.self) { index in
                AccessibilityRotorEntry("Image \(index + 1)", id: index)
            }
        }
        .onChange(of: viewModel.selectedImage) { oldValue, newValue in
            if let newValue = newValue {
                viewModel.handleCameraImage(newValue)
            }
        }
        .onChange(of: viewModel.selectedImages) { oldValue, newValue in
            if !newValue.isEmpty {
                viewModel.handlePickerImages(newValue)
            }
        }
    }
}

// MARK: - Error Overlay View
struct ErrorOverlayView: View {
    let error: Error
    let partialResultsCount: Int
    let onContinue: () -> Void
    let onRetry: () -> Void
    let onDismiss: () -> Void

    var body: some View {
        VStack(spacing: 20) {
            // Error icon
            errorIcon

            // Error title
            errorTitle

            // Error description
            Text(error.localizedDescription)
                .font(.body)
                .multilineTextAlignment(.center)
                .padding(.horizontal)

            // Recovery suggestion
            if let scanError = error as? ScanError {
                Text(scanError.recoverySuggestion ?? "")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }

            // Action buttons
            actionButtons
        }
        .padding(24)
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 16))
        .padding(.horizontal, 32)
        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .center)
        .background(Color.black.opacity(0.3))
        .edgesIgnoringSafeArea(.all)
    }

    @ViewBuilder
    private var errorIcon: some View {
        if let scanError = error as? ScanError {
            switch scanError {
            case .partialVisionSuccess:
                Image(systemName: "checkmark.circle.trianglebadge.exclamationmark")
                    .font(.system(size: 48))
                    .foregroundColor(.orange)
            default:
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 48))
                    .foregroundColor(.red)
            }
        } else {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 48))
                .foregroundColor(.red)
        }
    }

    @ViewBuilder
    private var errorTitle: some View {
        if let scanError = error as? ScanError {
            switch scanError {
            case .partialVisionSuccess:
                Text("Partial Success")
                    .font(.title2)
                    .fontWeight(.semibold)
            default:
                Text("Processing Error")
                    .font(.title2)
                    .fontWeight(.semibold)
            }
        } else {
            Text("Processing Error")
                .font(.title2)
                .fontWeight(.semibold)
        }
    }

    @ViewBuilder
    private var actionButtons: some View {
        if let scanError = error as? ScanError {
            HStack(spacing: 16) {
                // Show "Continue" button for partial success
                if scanError.allowsContinue && partialResultsCount > 0 {
                    Button("Continue with \(partialResultsCount) items") {
                        onContinue()
                    }
                    .buttonStyle(BorderedProminentButtonStyle())
                }

                // Show "Retry" button if retryable
                if scanError.canRetry {
                    if scanError.allowsContinue {
                        Button("Retry") {
                            onRetry()
                        }
                        .buttonStyle(BorderedButtonStyle())
                    } else {
                        Button("Retry") {
                            onRetry()
                        }
                        .buttonStyle(BorderedProminentButtonStyle())
                    }
                }

                // Always show dismiss button
                Button("Dismiss") {
                    onDismiss()
                }
                .buttonStyle(BorderedButtonStyle())
            }
        } else {
            // Fallback for non-ScanError types
            HStack(spacing: 16) {
                Button("Retry") {
                    onRetry()
                }
                .buttonStyle(BorderedProminentButtonStyle())

                Button("Dismiss") {
                    onDismiss()
                }
                .buttonStyle(BorderedButtonStyle())
            }
        }
    }
}

#Preview {
    StagingView(viewModel: StagingViewModel(navigationCoordinator: NavigationCoordinator()))
}