import SwiftUI

/// Allergies & Intolerances管理页面
/// 
/// 用于管理用户的过敏和不耐受食物
struct AllergiesIntolerancesView: View {
    
    @Environment(AuthenticationService.self) private var authService: AuthenticationService
    @Environment(\.dismiss) private var dismiss
    
    @State private var selectedAllergies: Set<AllergyIntolerance> = []
    @State private var hasUnsavedChanges = false
    
    // MARK: - New Callback-Based Properties (Task 24)
    
    let initialAllergies: [AllergyIntolerance]?
    let onSave: (([AllergyIntolerance]) async -> Void)?
    
    // Available allergy options
    private let availableAllergies: [AllergyIntolerance] = AllergyIntolerance.allCases
    
    // MARK: - Initializers
    
    /// Default initializer for backward compatibility
    init() {
        self.initialAllergies = nil
        self.onSave = nil
    }
    
    /// New callback-based initializer for Task 24 integration
    init(initialAllergies: [AllergyIntolerance], onSave: @escaping ([AllergyIntolerance]) async -> Void) {
        self.initialAllergies = initialAllergies
        self.onSave = onSave
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerSection
                
                // Allergies List
                ScrollView {
                    LazyVStack(spacing: 12) {
                        ForEach(availableAllergies, id: \.self) { allergy in
                            allergyRow(allergy)
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
            }
            .background(Color(.systemGroupedBackground))
            .navigationTitle("Allergies & Intolerances")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        if let onSave = onSave {
                            // Use callback-based saving (Task 24)
                            Task {
                                await onSave(Array(selectedAllergies))
                            }
                        } else {
                            // Fallback to legacy saving
                            saveAllergies()
                            dismiss()
                        }
                    }
                    .font(.body.weight(.medium))
                    .foregroundStyle(.blue)
                }
            }
        }
        .onAppear {
            loadCurrentAllergies()
        }
    }
    
    // MARK: - Header Section
    
    @ViewBuilder
    private var headerSection: some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 50))
                .foregroundStyle(.orange.gradient)
            
            VStack(spacing: 8) {
                Text("Medical Restrictions")
                    .font(.title2.weight(.semibold))
                    .foregroundStyle(.primary)
                
                Text("Select any allergies or intolerances that affect your food choices")
                    .font(.subheadline)
                    .foregroundStyle(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }
        }
        .padding(.vertical, 20)
        .background(Color(.systemBackground))
    }
    
    // MARK: - Allergy Row
    
    @ViewBuilder
    private func allergyRow(_ allergy: AllergyIntolerance) -> some View {
        Button {
            toggleAllergy(allergy)
        } label: {
            HStack(spacing: 16) {
                // Icon
                Text(allergy.icon)
                    .font(.title2)
                    .frame(width: 40, height: 40)
                    .background(
                        Circle()
                            .fill(selectedAllergies.contains(allergy) ? .orange.opacity(0.1) : .gray.opacity(0.1))
                    )
                
                // Content
                VStack(alignment: .leading, spacing: 4) {
                    Text(allergy.rawValue)
                        .font(.body.weight(.medium))
                        .foregroundStyle(.primary)
                        .multilineTextAlignment(.leading)
                    
                    Text(allergy.description)
                        .font(.caption)
                        .foregroundStyle(.secondary)
                        .multilineTextAlignment(.leading)
                }
                
                Spacer()
                
                // Selection Indicator
                if selectedAllergies.contains(allergy) {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.title2)
                        .foregroundStyle(.orange)
                } else {
                    Image(systemName: "circle")
                        .font(.title2)
                        .foregroundStyle(.gray)
                }
            }
            .padding(16)
            .background(Color(.systemBackground))
            .clipShape(RoundedRectangle(cornerRadius: 12))
            .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
        }
        .buttonStyle(.plain)
    }
    
    // MARK: - Helper Methods
    
    private func toggleAllergy(_ allergy: AllergyIntolerance) {
        if selectedAllergies.contains(allergy) {
            selectedAllergies.remove(allergy)
        } else {
            selectedAllergies.insert(allergy)
        }
        hasUnsavedChanges = true
    }
    
    private func loadCurrentAllergies() {
        if let initialAllergies = initialAllergies {
            // Use provided initial allergies (Task 24)
            selectedAllergies = Set(initialAllergies)
        } else if let preferences = authService.userPreferences {
            // Fallback to legacy loading
            selectedAllergies = Set(preferences.allergiesIntolerances)
        }
    }
    
    private func saveAllergies() {
        var preferences = authService.userPreferences ?? UserPreferences.createDefault(for: authService.currentUser?.uid ?? "")
        preferences.allergiesIntolerances = Array(selectedAllergies)
        preferences.lastUpdated = Date()
        authService.userPreferences = preferences
        hasUnsavedChanges = false
    }
}

// MARK: - AllergyIntolerance Extension

extension AllergyIntolerance {
    var description: String {
        switch self {
        case .nuts:
            return "Tree nuts and peanuts"
        case .shellfish:
            return "Shrimp, crab, lobster, etc."
        case .dairy:
            return "Milk, cheese, yogurt"
        case .eggs:
            return "Chicken eggs and egg products"
        case .soy:
            return "Soybeans and soy products"
        case .wheat:
            return "Wheat and wheat-based products"
        case .fish:
            return "All types of fish"
        case .sesame:
            return "Sesame seeds and oil"
        case .sulfites:
            return "Preservatives in wine, dried fruits"
        case .corn:
            return "Corn and corn-based products"
        case .chocolate:
            return "Cocoa and chocolate products"
        case .tomatoes:
            return "Tomatoes and tomato-based products"
        case .citrus:
            return "Oranges, lemons, limes, etc."
        case .mushrooms:
            return "All types of mushrooms"
        case .onions:
            return "Onions and related vegetables"
        case .garlic:
            return "Garlic and garlic-based seasonings"
        case .spices:
            return "Various spices and seasonings"
        case .caffeine:
            return "Coffee, tea, energy drinks"
        case .alcohol:
            return "All alcoholic beverages"
        case .artificial_sweeteners:
            return "Aspartame, sucralose, etc."
        }
    }
}

#Preview {
    AllergiesIntolerancesView()
        .environment(AuthenticationService())
} 