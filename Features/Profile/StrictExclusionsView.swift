import SwiftUI

/// Strict Exclusions管理页面
/// 
/// 用于管理绝对不能使用的食材
struct StrictExclusionsView: View {
    
    @Environment(AuthenticationService.self) private var authService: AuthenticationService
    @Environment(\.dismiss) private var dismiss
    
    @State private var selectedExclusions: Set<StrictExclusion> = []
    @State private var hasUnsavedChanges = false
    
    // MARK: - New Callback-Based Properties (Task 24)
    
    let initialExclusions: [StrictExclusion]?
    let onSave: (([StrictExclusion]) async -> Void)?
    
    // Available exclusion options
    private let availableExclusions: [StrictExclusion] = StrictExclusion.allCases
    
    // MARK: - Initializers
    
    /// Default initializer for backward compatibility
    init() {
        self.initialExclusions = nil
        self.onSave = nil
    }
    
    /// New callback-based initializer for Task 24 integration
    init(initialExclusions: [StrictExclusion], onSave: @escaping ([StrictExclusion]) async -> Void) {
        self.initialExclusions = initialExclusions
        self.onSave = onSave
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerSection
                
                // Exclusions List
                ScrollView {
                    LazyVStack(spacing: 12) {
                        ForEach(availableExclusions, id: \.self) { exclusion in
                            exclusionRow(exclusion)
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
            }
            .background(Color(.systemGroupedBackground))
            .navigationTitle("Strict Exclusions")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        if let onSave = onSave {
                            // Use callback-based saving (Task 24)
                            Task {
                                await onSave(Array(selectedExclusions))
                            }
                        } else {
                            // Fallback to legacy saving
                            saveExclusions()
                            dismiss()
                        }
                    }
                    .font(.body.weight(.medium))
                    .foregroundStyle(.blue)
                }
            }
        }
        .onAppear {
            loadCurrentExclusions()
        }
    }
    
    // MARK: - Header Section
    
    @ViewBuilder
    private var headerSection: some View {
        VStack(spacing: 16) {
            Image(systemName: "xmark.circle.fill")
                .font(.system(size: 50))
                .foregroundStyle(.red.gradient)
            
            VStack(spacing: 8) {
                Text("Foods to Never Use")
                    .font(.title2.weight(.semibold))
                    .foregroundStyle(.primary)
                
                Text("Select ingredients that should never appear in your recipes")
                    .font(.subheadline)
                    .foregroundStyle(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }
        }
        .padding(.vertical, 20)
        .background(Color(.systemBackground))
    }
    
    // MARK: - Exclusion Row
    
    @ViewBuilder
    private func exclusionRow(_ exclusion: StrictExclusion) -> some View {
        Button {
            toggleExclusion(exclusion)
        } label: {
            HStack(spacing: 16) {
                // Icon
                Text(exclusion.icon)
                    .font(.title2)
                    .frame(width: 40, height: 40)
                    .background(
                        Circle()
                            .fill(selectedExclusions.contains(exclusion) ? .red.opacity(0.1) : .gray.opacity(0.1))
                    )
                
                // Content
                VStack(alignment: .leading, spacing: 4) {
                    Text(exclusion.rawValue)
                        .font(.body.weight(.medium))
                        .foregroundStyle(.primary)
                        .multilineTextAlignment(.leading)
                    
                    Text(exclusion.description)
                        .font(.caption)
                        .foregroundStyle(.secondary)
                        .multilineTextAlignment(.leading)
                }
                
                Spacer()
                
                // Selection Indicator
                if selectedExclusions.contains(exclusion) {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.title2)
                        .foregroundStyle(.red)
                } else {
                    Image(systemName: "circle")
                        .font(.title2)
                        .foregroundStyle(.gray)
                }
            }
            .padding(16)
            .background(Color(.systemBackground))
            .clipShape(RoundedRectangle(cornerRadius: 12))
            .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
        }
        .buttonStyle(.plain)
    }
    
    // MARK: - Helper Methods
    
    private func toggleExclusion(_ exclusion: StrictExclusion) {
        if selectedExclusions.contains(exclusion) {
            selectedExclusions.remove(exclusion)
        } else {
            selectedExclusions.insert(exclusion)
        }
        hasUnsavedChanges = true
    }
    
    private func loadCurrentExclusions() {
        if let initialExclusions = initialExclusions {
            // Use provided initial exclusions (Task 24)
            selectedExclusions = Set(initialExclusions)
        } else if let preferences = authService.userPreferences {
            // Fallback to legacy loading
            selectedExclusions = Set(preferences.strictExclusions)
        }
    }
    
    private func saveExclusions() {
        var preferences = authService.userPreferences ?? UserPreferences.createDefault(for: authService.currentUser?.uid ?? "")
        preferences.strictExclusions = Array(selectedExclusions)
        preferences.lastUpdated = Date()
        authService.userPreferences = preferences
        hasUnsavedChanges = false
    }
}

// MARK: - StrictExclusion Extension

extension StrictExclusion {
    var description: String {
        switch self {
        case .nuts:
            return "All tree nuts and peanuts"
        case .shellfish:
            return "Shrimp, crab, lobster, etc."
        case .alcohol:
            return "Wine, beer, spirits in cooking"
        case .pork:
            return "Pork products and bacon"
        case .beef:
            return "Beef and beef products"
        case .dairy:
            return "Milk, cheese, butter, cream"
        case .eggs:
            return "Chicken eggs and egg products"
        case .soy:
            return "Soy sauce, tofu, soy products"
        case .gluten:
            return "Wheat, barley, rye products"
        case .seafood:
            return "Fish and all seafood"
        case .chicken:
            return "Chicken and poultry products"
        case .turkey:
            return "Turkey and turkey products"
        case .lamb:
            return "Lamb and mutton products"
        case .fish:
            return "Fresh and saltwater fish"
        case .peanuts:
            return "Peanuts and peanut products"
        case .wheat:
            return "Wheat flour and wheat products"
        case .corn:
            return "Corn and corn-based products"
        case .mushrooms:
            return "All types of mushrooms"
        case .onions:
            return "Onions and onion powder"
        case .garlic:
            return "Garlic and garlic powder"
        case .spicyFood:
            return "Hot peppers and spicy seasonings"
        case .caffeine:
            return "Coffee, tea, chocolate with caffeine"
        case .artificialSweeteners:
            return "Aspartame, sucralose, etc."
        case .preservatives:
            return "Chemical preservatives and additives"
        case .processedFoods:
            return "Pre-packaged and processed foods"
        }
    }
}

#Preview {
    StrictExclusionsView()
        .environment(AuthenticationService())
} 