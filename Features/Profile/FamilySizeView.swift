import SwiftUI

/// Family Size管理页面
/// 
/// 用于设置家庭人数，影响食谱的分量
struct FamilySizeView: View {
    
    @Environment(AuthenticationService.self) private var authService: AuthenticationService
    @Environment(\.dismiss) private var dismiss
    
    @State private var selectedFamilySize: Int = 2
    @State private var hasUnsavedChanges = false
    
    // MARK: - New Callback-Based Properties (Task 24)
    
    let initialFamilySize: Int?
    let onSave: ((Int) async -> Void)?
    
    // Family size options
    private let familySizeOptions = Array(1...12)
    
    // MARK: - Initializers
    
    /// Default initializer for backward compatibility
    init() {
        self.initialFamilySize = nil
        self.onSave = nil
    }
    
    /// New callback-based initializer for Task 24 integration
    init(initialFamilySize: Int, onSave: @escaping (Int) async -> Void) {
        self.initialFamilySize = initialFamilySize
        self.onSave = onSave
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerSection
                
                // Family Size Picker
                ScrollView {
                    VStack(spacing: 20) {
                        // Current Selection Display
                        currentSelectionDisplay
                        
                        // Size Options Grid
                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: 12) {
                            ForEach(familySizeOptions, id: \.self) { size in
                                familySizeButton(size)
                            }
                        }
                        .padding(.horizontal, 20)
                        
                        // Custom Input Section
                        customInputSection
                    }
                    .padding(.vertical, 20)
                }
            }
            .background(Color(.systemGroupedBackground))
            .navigationTitle("Family Size")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        if let onSave = onSave {
                            // Use callback-based saving (Task 24)
                            Task {
                                await onSave(selectedFamilySize)
                            }
                        } else {
                            // Fallback to legacy saving
                            saveFamilySize()
                            dismiss()
                        }
                    }
                    .font(.body.weight(.medium))
                    .foregroundStyle(.blue)
                }
            }
        }
        .onAppear {
            loadCurrentFamilySize()
        }
    }
    
    // MARK: - Header Section
    
    @ViewBuilder
    private var headerSection: some View {
        VStack(spacing: 16) {
            Image(systemName: "person.2.fill")
                .font(.system(size: 50))
                .foregroundStyle(.blue.gradient)
            
            VStack(spacing: 8) {
                Text("Family Size")
                    .font(.title2.weight(.semibold))
                    .foregroundStyle(.primary)
                
                Text("How many people do you typically cook for?")
                    .font(.subheadline)
                    .foregroundStyle(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }
        }
        .padding(.vertical, 20)
        .background(Color(.systemBackground))
    }
    
    // MARK: - Current Selection Display
    
    @ViewBuilder
    private var currentSelectionDisplay: some View {
        VStack(spacing: 12) {
            Text("\(selectedFamilySize)")
                .font(.system(size: 60, weight: .bold, design: .rounded))
                .foregroundStyle(.blue)
            
            Text(selectedFamilySize == 1 ? "person" : "people")
                .font(.title3.weight(.medium))
                .foregroundStyle(.secondary)
        }
        .padding(.vertical, 20)
        .frame(maxWidth: .infinity)
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
        .padding(.horizontal, 20)
    }
    
    // MARK: - Family Size Button
    
    @ViewBuilder
    private func familySizeButton(_ size: Int) -> some View {
        Button {
            selectedFamilySize = size
            hasUnsavedChanges = true
        } label: {
            VStack(spacing: 8) {
                Text("\(size)")
                    .font(.title2.weight(.semibold))
                    .foregroundStyle(selectedFamilySize == size ? .white : .primary)
                
                Text(size == 1 ? "person" : "people")
                    .font(.caption)
                    .foregroundStyle(selectedFamilySize == size ? .white.opacity(0.8) : .secondary)
            }
            .frame(height: 80)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(selectedFamilySize == size ? .blue : Color(.systemBackground))
            )
            .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
        }
        .buttonStyle(.plain)
    }
    
    // MARK: - Custom Input Section
    
    @ViewBuilder
    private var customInputSection: some View {
        VStack(spacing: 16) {
            Text("Need a different size?")
                .font(.subheadline.weight(.medium))
                .foregroundStyle(.secondary)
            
            HStack(spacing: 20) {
                Button {
                    if selectedFamilySize > 1 {
                        selectedFamilySize -= 1
                        hasUnsavedChanges = true
                    }
                } label: {
                    Image(systemName: "minus.circle.fill")
                        .font(.title2)
                        .foregroundStyle(selectedFamilySize > 1 ? .blue : .gray)
                }
                .disabled(selectedFamilySize <= 1)
                
                Text("\(selectedFamilySize)")
                    .font(.title.weight(.semibold))
                    .foregroundStyle(.primary)
                    .frame(minWidth: 60)
                
                Button {
                    if selectedFamilySize < 20 {
                        selectedFamilySize += 1
                        hasUnsavedChanges = true
                    }
                } label: {
                    Image(systemName: "plus.circle.fill")
                        .font(.title2)
                        .foregroundStyle(selectedFamilySize < 20 ? .blue : .gray)
                }
                .disabled(selectedFamilySize >= 20)
            }
            .padding(.horizontal, 32)
            .padding(.vertical, 16)
            .background(Color(.systemBackground))
            .clipShape(RoundedRectangle(cornerRadius: 16))
            .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
        }
        .padding(.horizontal, 20)
        .padding(.top, 20)
    }
    
    // MARK: - Helper Methods
    
    private func loadCurrentFamilySize() {
        if let initialFamilySize = initialFamilySize {
            // Use provided initial family size (Task 24)
            selectedFamilySize = initialFamilySize
        } else if let preferences = authService.userPreferences {
            // Fallback to legacy loading
            selectedFamilySize = preferences.familySize
        }
    }
    
    private func saveFamilySize() {
        var preferences = authService.userPreferences ?? UserPreferences.createDefault(for: authService.currentUser?.uid ?? "")
        preferences.familySize = selectedFamilySize
        preferences.lastUpdated = Date()
        authService.userPreferences = preferences
        hasUnsavedChanges = false
    }
}

// MARK: - Preview

#Preview {
    FamilySizeView()
        .environment(AuthenticationService())
} 