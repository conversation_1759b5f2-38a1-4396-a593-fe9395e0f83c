import SwiftUI
import UIKit

// MARK: - DEPRECATED VIEW MODEL
// ⚠️ This view model is deprecated and will be removed in a future version.
// 
// MIGRATION GUIDE:
// Instead of BatchProcessingViewModel, use the enhanced StagingViewModel which now includes:
// - Vision batch processing capabilities
// - Gemini ingredient canonicalization
// - Error handling for partial success scenarios
// - Progress tracking and overlay management
//
// This view model is maintained for backward compatibility during the transition period.

@available(*, deprecated, message: "Use enhanced StagingViewModel with integrated processing instead. This view model will be removed in a future version.")
@Observable
@MainActor
class BatchProcessingViewModel {
    private weak var navigationCoordinator: NavigationCoordinator?
    private let images: [UIImage]
    private let visionService = GoogleVisionAPIService()
    private let concurrencyManager = ConcurrencyManager.shared
    
    var isLoading = true
    var errorMessage: String?
    var processedCount = 0
    var totalCount: Int
    
    var progressText: String {
        "Processing image \(processedCount + 1) of \(totalCount)..."
    }
    
    init(navigationCoordinator: NavigationCoordinator, images: [UIImage]) {
        self.navigationCoordinator = navigationCoordinator
        self.images = images
        self.totalCount = images.count
    }
    
    func processImages() async {
        isLoading = true
        errorMessage = nil
        
        do {
            let results = try await concurrencyManager.processBatchImages(
                images,
                using: visionService
            ) { [weak self] processed, total in
                guard let self = self else { return }
                self.processedCount = processed
            }
            
            // Convert string results to ingredients for navigation
            let ingredients = results.map { result in
                Ingredient(name: result, category: .other)
            }
            
            // Navigate to results with ingredients
            isLoading = false
            navigationCoordinator?.navigateToResults(ingredients: ingredients)
            
        } catch {
            isLoading = false
            errorMessage = error.localizedDescription
        }
    }
} 