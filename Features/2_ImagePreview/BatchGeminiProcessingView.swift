import SwiftUI

// MARK: - DEPRECATED VIEW
// ⚠️ This view is deprecated and will be removed in a future version.
// 
// MIGRATION GUIDE:
// Gemini processing is now handled internally within StagingView.
// The enhanced StagingView provides:
// - Integrated Vision → Gemini processing pipeline
// - Ingredient canonicalization with strict category enforcement
// - Error handling and partial success support
// - Direct navigation to ResultsView with processed ingredients
//
// This view is maintained for backward compatibility during the transition period.

@available(*, deprecated, message: "Gemini processing is now handled internally in StagingView. This view will be removed in a future version.")
struct BatchGeminiProcessingView: View {
    let images: [UIImage]
    let visionResponses: [String]
    @Environment(NavigationCoordinator.self) var coordinator: NavigationCoordinator
    @State private var viewModel = BatchGeminiProcessingViewModel()
    @State private var processingError: Error?
    @State private var showError = false
    
    var body: some View {
        VStack(spacing: 30) {
            Spacer()
            
            if viewModel.isLoading {
                ProgressView(value: viewModel.processingProgress)
                    .progressViewStyle(.linear)
                    .padding(.horizontal, 40)
                
                ProgressView()
                    .scaleEffect(2)
                    .progressViewStyle(CircularProgressViewStyle())
                
                Text("Analyzing ingredients...")
                    .font(.title2.weight(.semibold))
                    .padding(.top, 30)
                
                Text("Using Gemini AI to identify food items from all detected text")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
                
                Text("\(Int(viewModel.processingProgress * 100))%")
                    .font(.headline)
                    .foregroundColor(.blue)
                    .padding(.top, 10)
                    
            } else if let errorMessage = viewModel.errorMessage {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.red)
                
                Text("Processing Error")
                    .font(.title.weight(.bold))
                
                Text(errorMessage)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
                    
                Button("Retry") {
                    // Task will automatically restart due to id dependency
                    processingError = nil
                    showError = false
                }
                .buttonStyle(.borderedProminent)
                .padding(.top)
                
                Button("Cancel") {
                    coordinator.navigateToStaging()
                }
                .buttonStyle(.bordered)
            }
            
            Spacer()
        }
        .navigationTitle("AI Analysis")
        .navigationBarTitleDisplayMode(.inline)
        .task(id: visionResponses) { // Will restart if responses change
            do {
                // Set up viewModel with current data
                viewModel.visionResponses = visionResponses
                viewModel.images = images
                
                // Extract ingredients using AI
                let ingredients = try await viewModel.extractIngredients()
                
                // Only navigate if task wasn't cancelled
                if !Task.isCancelled {
                    #if DEBUG
                    // In debug mode, navigate to debug view first
                    let debugInfo = viewModel.getDebugInfo()
                    coordinator.navigateToDebug(
                        visionResponse: debugInfo.visionResponse,
                        geminiResponse: debugInfo.geminiResponse,
                        ingredients: ingredients
                    )
                    #else
                    // In release mode, go directly to results
                    coordinator.navigateToResults(ingredients: ingredients)
                    #endif
                }
            } catch {
                if !Task.isCancelled {
                    processingError = error
                    showError = true
                }
            }
        }
        .alert("Analysis Error", isPresented: $showError, presenting: processingError) { error in
            Button("Retry") {
                // Task will automatically restart
                processingError = nil
                showError = false
            }
            Button("Cancel", role: .cancel) {
                coordinator.navigateToStaging()
            }
        } message: { error in
            Text(error.localizedDescription)
        }
    }
} 