import SwiftUI
import UIKit

// MARK: - DEPRECATED VIEW MODEL
// ⚠️ This view model is deprecated and will be removed in a future version.
// 
// MIGRATION GUIDE:
// Vision results processing is now integrated into StagingViewModel.
// The enhanced flow eliminates the need for separate vision results handling.
//
// This view model is maintained for backward compatibility during the transition period.

@available(*, deprecated, message: "Vision processing is now integrated into StagingViewModel. This view model will be removed in a future version.")
@Observable
@MainActor
class BatchVisionResultsViewModel {
    private weak var navigationCoordinator: NavigationCoordinator?
    let images: [UIImage]
    let visionResponses: [String]
    var currentImageIndex = 0
    
    var currentImage: UIImage {
        images[currentImageIndex]
    }
    
    var currentVisionResponse: String {
        visionResponses[currentImageIndex]
    }
    
    var imageCountText: String {
        "Image \(currentImageIndex + 1) of \(images.count)"
    }
    
    var canGoToPrevious: Bool {
        currentImageIndex > 0
    }
    
    var canGoToNext: Bool {
        currentImageIndex < images.count - 1
    }
    
    var hasDetectedText: Bool {
        !visionResponses.allSatisfy { $0.isEmpty }
    }
    
    init(navigationCoordinator: NavigationCoordinator, images: [UIImage], visionResponses: [String]) {
        self.navigationCoordinator = navigationCoordinator
        self.images = images
        self.visionResponses = visionResponses
    }
    
    func goToPreviousImage() {
        guard canGoToPrevious else { return }
        currentImageIndex -= 1
    }
    
    func goToNextImage() {
        guard canGoToNext else { return }
        currentImageIndex += 1
    }
    
    func proceedToGemini() {
        navigationCoordinator?.navigateToBatchGeminiProcessing(images: images, visionResponses: visionResponses)
    }
    
    func returnToStaging() {
        navigationCoordinator?.navigateToStaging()
    }
} 