import SwiftUI
import UIKit

// MARK: - DEPRECATED VIEW MODEL
// ⚠️ This view model is deprecated and will be removed in a future version.
// 
// MIGRATION GUIDE:
// Gemini processing is now integrated into StagingViewModel.
// The enhanced StagingViewModel provides:
// - Direct ingredient canonicalization from Vision outputs
// - Strict category enforcement with PantryCategory.rawValue
// - Error handling and partial success support
// - Progress tracking and overlay management
//
// This view model is maintained for backward compatibility during the transition period.

@available(*, deprecated, message: "Gemini processing is now integrated into StagingViewModel. This view model will be removed in a future version.")
@Observable
@MainActor
class BatchGeminiProcessingViewModel {
    var images: [UIImage] = []
    var visionResponses: [String] = []
    private let geminiService = GeminiAPIService()
    
    var isLoading = true
    var errorMessage: String?
    var processingProgress: Double = 0.0
    var extractedIngredients: [Ingredient] = []
    
    init() {
        // Default empty initialization - data will be set by the view
    }
    
    /// Extract ingredients from vision responses using Gemini AI
    /// Returns the extracted ingredients for the view to handle navigation
    func extractIngredients() async throws -> [Ingredient] {
        isLoading = true
        errorMessage = nil
        processingProgress = 0.0
        
        do {
            // Check for cancellation before processing
            if Task.isCancelled { 
                throw TaskCancellationError()
            }
            
            processingProgress = 0.2
            
            // Aggregate all vision responses into one text
            let aggregatedText = visionResponses
                .enumerated()
                .map { index, text in
                    "=== Image \(index + 1) ===\n\(text)"
                }
                .joined(separator: "\n\n")
            
            processingProgress = 0.4
            
            // Check for cancellation before API call
            if Task.isCancelled { 
                throw TaskCancellationError()
            }
            
            // Call Gemini API with aggregated text
            let ingredients = try await geminiService.extractIngredients(from: aggregatedText)
            
            processingProgress = 0.8
            
            // Check for cancellation before processing results
            if Task.isCancelled { 
                throw TaskCancellationError()
            }
            
            // Remove duplicates while preserving order and categories
            let uniqueIngredients = ingredients.reduce(into: [Ingredient]()) { result, ingredient in
                if !result.contains(where: { $0.name.caseInsensitiveCompare(ingredient.name) == .orderedSame }) {
                    result.append(ingredient)
                }
            }
            
            processingProgress = 1.0
            extractedIngredients = uniqueIngredients
            isLoading = false
            
            return uniqueIngredients
            
        } catch {
            isLoading = false
            if Task.isCancelled {
                throw TaskCancellationError()
            } else {
                errorMessage = error.localizedDescription
                throw error
            }
        }
    }
    
    /// Get debug information for development builds
    func getDebugInfo() -> (visionResponse: String, geminiResponse: String) {
        let aggregatedText = visionResponses
            .enumerated()
            .map { index, text in
                "=== Image \(index + 1) ===\n\(text)"
            }
            .joined(separator: "\n\n")
        
        let geminiResponse = extractedIngredients.map { "\($0.name) (\($0.category.rawValue))" }.joined(separator: ", ")
        
        return (visionResponse: aggregatedText, geminiResponse: geminiResponse)
    }
}

// MARK: - Custom Error Types are now in Models/Ingredient.swift 