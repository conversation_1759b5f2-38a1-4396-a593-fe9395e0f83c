import SwiftUI

// MARK: - DEPRECATED VIEW
// ⚠️ This view is deprecated and will be removed in a future version.
// 
// MIGRATION GUIDE:
// Instead of using BatchProcessingView, use the enhanced StagingView which now supports:
// - Up to 10 images (increased from 3)
// - Direct Vision → Gemini processing pipeline
// - Processing overlay with progress indication
// - Direct navigation to ResultsView
//
// The new flow: StagingView → (internal processing) → ResultsView
// Old flow: StagingView → BatchProcessingView → BatchVisionResultsView → BatchGeminiProcessingView → ResultsView
//
// This view is maintained for backward compatibility during the transition period.

@available(*, deprecated, message: "Use enhanced StagingView with direct processing instead. This view will be removed in a future version.")
struct BatchProcessingView: View {
    @State var viewModel: BatchProcessingViewModel
    
    var body: some View {
        VStack(spacing: 30) {
            Spacer()
            
            if viewModel.isLoading {
                ProgressView()
                    .scaleEffect(2)
                    .progressViewStyle(CircularProgressViewStyle())
                
                Text(viewModel.progressText)
                    .font(.title2.weight(.semibold))
                    .padding(.top, 30)
                
                Text("Detecting text in your images")
                    .font(.body)
                    .foregroundColor(.secondary)
                
                // Progress indicator
                ProgressView(value: Double(viewModel.processedCount), total: Double(viewModel.totalCount))
                    .progressViewStyle(LinearProgressViewStyle())
                    .padding(.horizontal, 50)
                    .padding(.top, 20)
                
            } else if let errorMessage = viewModel.errorMessage {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.red)
                
                Text("Error")
                    .font(.title.weight(.bold))
                
                Text(errorMessage)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
            
            Spacer()
        }
        .navigationTitle("Processing")
        .navigationBarTitleDisplayMode(.inline)
        .task(id: viewModel.totalCount) {
            await viewModel.processImages()
        }
    }
} 