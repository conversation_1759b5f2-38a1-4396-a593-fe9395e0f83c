import SwiftUI

@Observable
@MainActor
class RecipeGeneratorViewModel {
    private let recipeService: RecipeGenerationService
    private let authService: AuthenticationService
    
    init(
        recipeService: RecipeGenerationService = ServiceContainer.shared.recipeGenerationService,
        authService: AuthenticationService = ServiceContainer.shared.authenticationService
    ) {
        self.recipeService = recipeService
        self.authService = authService
    }
    
    var generatedRecipeIdeas: [Recipe] = []
    var isGenerating = false
    var errorMessage: String?
    
    /// Get user's dietary restrictions as strings
    var userDietaryRestrictions: [String] {
        if let preferences = authService.userPreferences {
            let restrictions = preferences.dietaryRestrictions.map { $0.rawValue }
            let allergies = preferences.allergies
            let intolerances = preferences.intolerances
            let strictExclusions = preferences.strictExclusions.map { $0.rawValue }
            
            let allRestrictions = restrictions + allergies + intolerances + strictExclusions
            return Array(Set(allRestrictions)) // Remove duplicates
        }
        return []
    }
    
    /// Generate user preferences context string for recipe generation
    var userPreferencesContext: String {
        guard let preferences = authService.userPreferences else { return "" }
        
        var context = ""
        
        // Family size
        context += "- Family size: \(preferences.familySize) people\n"
        
        // Dietary restrictions
        if !preferences.dietaryRestrictions.isEmpty {
            context += "- Dietary preferences: \(preferences.dietaryRestrictions.map { $0.rawValue }.joined(separator: ", "))\n"
        }
        
        // Allergies
        if !preferences.allergies.isEmpty {
            context += "- Allergies: \(preferences.allergies.joined(separator: ", "))\n"
        }
        
        // Intolerances
        if !preferences.intolerances.isEmpty {
            context += "- Intolerances: \(preferences.intolerances.joined(separator: ", "))\n"
        }
        
        // Strict exclusions
        if !preferences.strictExclusions.isEmpty {
            context += "- Must exclude: \(preferences.strictExclusions.map { $0.rawValue }.joined(separator: ", "))\n"
        }
        
        return context
    }
    
    /// Generate recipe ideas based on pantry ingredients and cooking time
    func generateRecipeIdeas(cookingTimeMinutes: Int) async {
        isGenerating = true
        errorMessage = nil
        
        do {
            // Get pantry ingredients
            let pantryService = ServiceContainer.shared.pantryService
            let ingredientNames = pantryService.pantryItems.map { $0.name }
            
            guard !ingredientNames.isEmpty else {
                errorMessage = "No ingredients found in pantry"
                isGenerating = false
                return
            }
            
            // Create recipe preferences
            let preferences: RecipePreferences
            if let userPrefs = authService.userPreferences {
                preferences = RecipePreferences(
                    from: userPrefs,
                    cookingTime: cookingTimeMinutes
                )
            } else {
                preferences = RecipePreferences(
                    cookingTimeInMinutes: cookingTimeMinutes,
                    numberOfServings: 2,
                    dietaryRestrictions: [],
                    allergiesAndIntolerances: [],
                    strictExclusions: [],
                    respectRestrictions: true
                )
            }
            
            // Generate recipe ideas using recipe generation service
            let recipeIdeas = try await recipeService.generateMealIdeas(from: ingredientNames, preferences: preferences)
            
            // Extract Recipe objects from RecipeIdea objects
            var recipes = recipeIdeas.map { $0.recipe }
            
            // Local safety filter as an extra guard
            if preferences.respectRestrictions {
                recipes = recipes.filter { isRecipeCompatible(with: $0) }
            }
            generatedRecipeIdeas = recipes
            
        } catch {
            errorMessage = "Failed to generate recipes: \(error.localizedDescription)"
        }
        
        isGenerating = false
    }
    
    /// Convert RecipeIdea to detailed Recipe
    func convertToDetailedRecipe(_ recipeIdea: Recipe) async throws -> Recipe {
        // Return the recipe as-is since it's already detailed
        return recipeIdea
    }
    
    /// Clear all generated recipes
    func clearRecipes() {
        generatedRecipeIdeas.removeAll()
        errorMessage = nil
    }
    
    /// Format cooking time for display
    func formatCookingTime(_ minutes: Int) -> String {
        if minutes < 60 {
            return "\(minutes) min"
        } else {
            let hours = minutes / 60
            let remainingMinutes = minutes % 60
            if remainingMinutes == 0 {
                return "\(hours) hr"
            } else {
                return "\(hours) hr \(remainingMinutes) min"
            }
        }
    }
    
    /// Check if recipe matches user preferences
    func isRecipeCompatible(with recipe: Recipe) -> Bool {
        guard authService.userPreferences != nil else { return true }
        
        // Check against strict exclusions and allergies
        let userRestrictions = Set(userDietaryRestrictions.map { $0.lowercased() })
        let recipeIngredients = Set(recipe.ingredients.map { $0.lowercased() })
        
        // If any user restriction appears in recipe ingredients, it's not compatible
        return userRestrictions.isDisjoint(with: recipeIngredients)
    }
}