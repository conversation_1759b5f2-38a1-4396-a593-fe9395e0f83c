import SwiftUI

struct RecipeGeneratorView: View {
    @State private var viewModel = RecipeGeneratorViewModel(
        recipeService: ServiceContainer.shared.recipeGenerationService,
        authService: ServiceContainer.shared.authenticationService
    )
    @Environment(PantryService.self) var pantryService: PantryService
    @Environment(AuthenticationService.self) var authService: AuthenticationService
    @Environment(NavigationCoordinator.self) var coordinator: NavigationCoordinator
    @State private var showError = false
    @State private var cookingTime = 30
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Empty pantry check
                if pantryService.pantryItems.isEmpty {
                    emptyPantryView
                        .frame(maxWidth: .infinity)
                        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
                        .padding()
                } else {
                    // Recipe generation content
                    recipeGenerationContent
                        .padding()
                }
            }
        }
        .navigationTitle("Recipe Ideas")
        .navigationBarTitleDisplayMode(.large)
        .alert("Recipe Generation Error", isPresented: $showError) {
            Button("OK") { }
        } message: {
            Text(viewModel.errorMessage ?? "An unknown error occurred")
        }
    }
    
    private var emptyPantryView: some View {
        VStack(spacing: 16) {
            Image(systemName: "basket")
                .font(.system(size: 60))
                .foregroundColor(.secondary)
            
            Text("Your Pantry is Empty")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("Add some ingredients to your pantry to get personalized recipe suggestions.")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button("Go to Pantry") {
                // Switch to pantry tab instead of using navigateTo
                coordinator.switchToPantryTab()
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.large)
        }
        .padding()
    }
    
    private var recipeGenerationContent: some View {
        VStack(spacing: 24) {
            // Cooking time selector
            cookingTimeSelector
            
            // Generate recipes button
            generateRecipesButton
            
            // Generated recipes list
            if !viewModel.generatedRecipeIdeas.isEmpty {
                generatedRecipesList
            }
        }
    }
    
    private var cookingTimeSelector: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Cooking Time")
                .font(.headline)
            
            HStack {
                Text("\(cookingTime) minutes")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Slider(value: Binding(
                    get: { Double(cookingTime) },
                    set: { cookingTime = Int($0) }
                ), in: 15...120, step: 15)
            }
        }
        .padding()
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
    }
    
    private var generateRecipesButton: some View {
        Button(action: {
            Task {
                await viewModel.generateRecipeIdeas(cookingTimeMinutes: cookingTime)
                if viewModel.errorMessage != nil {
                    showError = true
                }
            }
        }) {
            HStack {
                if viewModel.isGenerating {
                    ProgressView()
                        .scaleEffect(0.8)
                } else {
                    Image(systemName: "sparkles")
                }
                
                Text(viewModel.isGenerating ? "Generating Ideas..." : "Generate Recipe Ideas")
            }
            .frame(maxWidth: .infinity)
        }
        .buttonStyle(.borderedProminent)
        .controlSize(.large)
        .disabled(viewModel.isGenerating || pantryService.pantryItems.isEmpty)
    }
    
    private var generatedRecipesList: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Recipe Ideas")
                .font(.headline)
            
            LazyVStack(spacing: 12) {
                ForEach(viewModel.generatedRecipeIdeas) { recipe in
                    // Use RecipeCard with Recipe instead of RecipeIdea
                    RecipeCard(recipe: recipe) {
                        // Navigate to recipe detail using the correct method
                        coordinator.navigateToRecipeDetail(recipe: recipe)
                    }
                }
            }
        }
    }
}

// Simple Recipe Card View for Recipe objects
struct RecipeCard: View {
    let recipe: Recipe
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(recipe.title)
                            .font(.headline)
                            .foregroundColor(.primary)
                            .multilineTextAlignment(.leading)
                        
                        Text(recipe.description)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .lineLimit(2)
                    }
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .foregroundColor(.secondary)
                }
                
                HStack {
                    Label("\(recipe.cookingTimeInMinutes) min", systemImage: "clock")
                    Spacer()
                    Label("\(recipe.numberOfServings) servings", systemImage: "person.2")
                    Spacer()
                    Label(recipe.difficulty.rawValue, systemImage: "star")
                }
                .font(.caption)
                .foregroundColor(.secondary)
            }
            .padding()
            .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
        }
        .buttonStyle(.plain)
    }
}

#Preview {
    NavigationStack {
        RecipeGeneratorView()
    }
    .environment(PantryService())
    .environment(AuthenticationService())
    .environment(NavigationCoordinator())
}