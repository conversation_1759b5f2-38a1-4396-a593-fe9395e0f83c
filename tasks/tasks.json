{"master": {"tasks": [{"id": 16, "title": "Validate UserPreferences Model for Firestore Compatibility", "description": "Analyze the existing UserPreferences model to ensure all properties are compatible with Firestore supported data types and implement Codable protocol support.", "details": "1. Review the current UserPreferences model structure\n2. Verify all property types against Firestore supported types (A<PERSON>y, Boolean, Bytes, Date and time, Float/Double, Geographical point, Integer, Map, Null, Reference, String)\n3. Ensure nested objects also conform to Firestore data types\n4. Implement Codable protocol:\n```swift\nextension UserPreferences: Codable {\n  enum CodingKeys: String, CodingKey {\n    case allergies, dietaryRestrictions, familySize, // add all properties\n  }\n  \n  func encode(to encoder: Encoder) throws {\n    var container = encoder.container(keyedBy: CodingKeys.self)\n    try container.encode(allergies, forKey: .allergies)\n    // encode all properties\n  }\n  \n  init(from decoder: Decoder) throws {\n    let container = try decoder.container(keyedBy: CodingKeys.self)\n    allergies = try container.decode([String].self, forKey: .allergies)\n    // decode all properties\n  }\n}\n```\n5. Add @DocumentID property wrapper for document ID if needed\n6. Create a default initializer for new users:\n```swift\nstatic func defaultPreferences() -> UserPreferences {\n  return UserPreferences(\n    allergies: [],\n    dietaryRestrictions: [],\n    familySize: 1,\n    // set reasonable defaults for all properties\n  )\n}\n```\n7. Use Firebase SDK version 10.15.0 or newer for best Swift Codable support", "testStrategy": "1. Create unit tests to verify Codable implementation works correctly\n2. Test encoding/decoding with sample data\n3. Verify default preferences initialization\n4. Test with edge cases (empty arrays, nil values where applicable)\n5. Verify serialization/deserialization maintains data integrity", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 17, "title": "Create UserProfileService Core Implementation", "description": "Implement the UserProfileService singleton responsible for all Firestore user data interactions, including saving and retrieving user preferences.", "status": "pending", "dependencies": [16], "priority": "high", "details": "Implement the UserProfileService exactly as specified in the Firestore_Implementation_Directive.md document section 2.2:\n\n```swift\nimport Foundation\nimport FirebaseFirestore\nimport FirebaseFirestoreSwift\n\n/// 用户配置服务 - 负责所有与Firestore用户数据交互的操作\nclass UserProfileService {\n    /// 共享单例实例\n    static let shared = UserProfileService()\n    \n    /// Firestore数据库引用\n    private let db = Firestore.firestore()\n    \n    /// 私有初始化方法确保单例模式\n    private init() {}\n    \n    /// 获取用户文档引用的辅助方法\n    /// - Parameter userId: 用户ID\n    /// - Returns: Firestore文档引用\n    private func userDocument(for userId: String) -> DocumentReference {\n        return db.collection(\"users\").document(userId)\n    }\n    \n    /// 将用户偏好保存到Firestore\n    /// - Parameters:\n    ///   - preferences: 用户偏好对象\n    ///   - userId: 用户ID\n    func savePreferences(_ preferences: UserPreferences, for userId: String) async throws {\n        do {\n            let docRef = userDocument(for: userId)\n            try await docRef.setData(from: preferences)\n        } catch {\n            logError(error, operation: \"savePreferences\")\n            throw mapError(error)\n        }\n    }\n    \n    /// 从Firestore获取用户偏好\n    /// - Parameter userId: 用户ID\n    /// - Returns: 用户偏好对象\n    func fetchPreferences(for userId: String) async throws -> UserPreferences {\n        do {\n            let docRef = userDocument(for: userId)\n            let document = try await docRef.getDocument()\n            \n            guard document.exists else {\n                throw UserProfileError.userNotFound\n            }\n            \n            return try document.data(as: UserPreferences.self)\n        } catch {\n            logError(error, operation: \"fetchPreferences\")\n            throw mapError(error)\n        }\n    }\n    \n    /// 获取或创建用户偏好\n    /// - Parameter userId: 用户ID\n    /// - Returns: 用户偏好对象（现有的或新创建的）\n    func fetchOrCreatePreferences(for userId: String) async throws -> UserPreferences {\n        do {\n            let docRef = userDocument(for: userId)\n            let document = try await docRef.getDocument()\n            \n            if document.exists {\n                return try document.data(as: UserPreferences.self)\n            } else {\n                let defaultPreferences = UserPreferences.defaultPreferences(for: userId)\n                try await savePreferences(defaultPreferences, for: userId)\n                return defaultPreferences\n            }\n        } catch {\n            logError(error, operation: \"fetchOrCreatePreferences\")\n            throw mapError(error)\n        }\n    }\n    \n    /// 将Firestore错误映射为应用特定错误\n    /// - Parameter error: 原始错误\n    /// - Returns: 映射后的UserProfileError\n    private func mapError(_ error: Error) -> UserProfileError {\n        if let firestoreError = error as? FirestoreError {\n            switch firestoreError {\n            case .notFound:\n                return .userNotFound\n            case .networkError:\n                return .networkError\n            default:\n                return .unknownError\n            }\n        } else if error is DecodingError {\n            return .decodingError\n        } else if let profileError = error as? UserProfileError {\n            return profileError\n        } else {\n            return .unknownError\n        }\n    }\n    \n    /// 记录错误信息用于调试\n    /// - Parameters:\n    ///   - error: 发生的错误\n    ///   - operation: 操作名称\n    private func logError(_ error: Error, operation: String) {\n        print(\"UserProfileService error during \\(operation): \\(error.localizedDescription)\")\n        // 在生产环境中，这里可以集成更高级的日志系统\n    }\n}\n\n/// 用户配置服务可能抛出的错误\nenum UserProfileError: Error {\n    /// 用户未找到\n    case userNotFound\n    /// 数据解码错误\n    case decodingError\n    /// 网络连接错误\n    case networkError\n    /// 未知错误\n    case unknownError\n}\n```", "testStrategy": "1. Create unit tests for UserProfileService using Firebase test helpers\n2. Mock Firestore responses for testing\n3. Test successful save/fetch operations\n4. Test error handling for network issues\n5. Test new user creation flow\n6. Verify singleton pattern works correctly\n7. Test with invalid user IDs and error conditions", "subtasks": [{"id": 1, "title": "Create UserProfileService.swift file with singleton pattern", "description": "Create the Services/UserProfileService.swift file and implement the singleton pattern with the Firestore database initialization and user document reference helper method.", "status": "pending", "dependencies": [], "details": "Create the file in the Services directory and implement the singleton pattern exactly as specified in the Firestore_Implementation_Directive.md document section 2.2. Include the shared instance, private database initialization, private initializer, and the userDocument helper method.", "testStrategy": "Verify the singleton pattern works correctly by accessing the shared instance multiple times and confirming it's the same instance. Test the userDocument helper method returns the correct Firestore document reference."}, {"id": 2, "title": "Implement core data operations methods", "description": "Implement the three core methods for saving and retrieving user preferences from Firestore: savePreferences, fetchPreferences, and fetchOrCreatePreferences.", "status": "pending", "dependencies": [1], "details": "Follow the exact implementation from the Firestore_Implementation_Directive.md document section 2.2 for these three methods. Ensure proper async/await handling and correct Firestore document operations for saving, fetching, and creating user preferences.", "testStrategy": "Create unit tests for each method using Firebase test helpers. Test successful save operations, fetch operations for existing users, and the create flow for new users. Mock Firestore responses to test various scenarios."}, {"id": 3, "title": "Implement UserProfileError enum", "description": "Create the UserProfileError enum to handle specific error cases that may occur during Firestore operations.", "status": "pending", "dependencies": [1], "details": "Implement the UserProfileError enum exactly as specified in the Firestore_Implementation_Directive.md document section 2.2, including all error cases: userNotFound, decodingError, networkError, and unknownError.", "testStrategy": "Test each error case by forcing conditions that would trigger them. Verify error messages are appropriate and that the errors are properly thrown and caught in the service methods."}, {"id": 4, "title": "Add error logging functionality", "description": "Implement the logError method for debugging purposes to track errors that occur during Firestore operations.", "status": "pending", "dependencies": [1, 3], "details": "Add the logError private method exactly as specified in the Firestore_Implementation_Directive.md document section 2.2. This method should take an Error object and an operation string, then print a formatted error message.", "testStrategy": "Test the logging functionality by triggering various error conditions and verifying that the correct error messages are logged. Use a mock logger or capture console output to verify logging works as expected."}, {"id": 5, "title": "Implement error mapping functionality", "description": "Add the mapError method to convert various error types to appropriate UserProfileError cases.", "status": "pending", "dependencies": [1, 3], "details": "Implement the mapError private method exactly as specified in the Firestore_Implementation_Directive.md document section 2.2. This method should take an Error object and return the appropriate UserProfileError case based on the error type.", "testStrategy": "Test the error mapping functionality by providing different error types and verifying they are correctly mapped to the appropriate UserProfileError cases."}, {"id": 6, "title": "Integrate error handling with core methods", "description": "Update the core methods to properly handle errors using the UserProfileError enum, mapError and logError methods.", "status": "pending", "dependencies": [2, 3, 4, 5], "details": "Modify the savePreferences, fetchPreferences, and fetchOrCreatePreferences methods to catch Firestore errors, map them to appropriate UserProfileError cases using the mapError method, log them using the logError method, and rethrow them for proper error propagation.", "testStrategy": "Test error handling by simulating various Firestore error conditions (network errors, permission issues, etc.) and verifying that they are properly caught, logged, mapped, and rethrown as appropriate UserProfileError cases."}]}, {"id": 18, "title": "Configure Firestore Security Rules", "description": "Implement and deploy Firestore security rules to ensure users can only access their own data and protect against unauthorized access.", "details": "1. Create firestore.rules file with the following security rules:\n```\nrules_version = '2';\nservice cloud.firestore {\n  match /databases/{database}/documents {\n    // Match any document in the 'users' collection\n    match /users/{userId} {\n      // Allow read/write only if the authenticated user ID matches the document ID\n      allow read, write: if request.auth != null && request.auth.uid == userId;\n      \n      // Additional validation rules for writes\n      allow write: if request.auth != null && \n                    request.auth.uid == userId && \n                    request.resource.data.keys().hasOnly(['allergies', 'dietaryRestrictions', 'familySize', /* all expected fields */]);\n    }\n    \n    // Deny access to all other collections by default\n    match /{document=**} {\n      allow read, write: if false;\n    }\n  }\n}\n```\n2. Test rules in the Firebase Console Rules Playground\n3. Deploy rules using Firebase CLI:\n```bash\nfirebase deploy --only firestore:rules\n```\n4. Document the security rules in project documentation\n5. Consider adding additional validation rules for specific fields if needed", "testStrategy": "1. Test security rules in Firebase Console Rules Playground\n2. Create test cases for:\n   - User accessing their own data (should succeed)\n   - User attempting to access another user's data (should fail)\n   - Unauthenticated access attempts (should fail)\n   - Write operations with invalid fields (should fail)\n3. Verify rules are correctly deployed\n4. Document test results and any edge cases", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 19, "title": "Implement Error Handling and Retry Mechanism", "description": "Create a robust error handling system for Firestore operations with appropriate user feedback and retry mechanisms for transient failures.", "details": "1. Create a dedicated error handling module:\n```swift\n// FirestoreError.swift\nenum FirestoreError: Error, LocalizedError {\n  case networkError\n  case permissionDenied\n  case documentNotFound\n  case serializationError\n  case unknownError(Error)\n  \n  var errorDescription: String? {\n    switch self {\n    case .networkError:\n      return NSLocalizedString(\"Network connection issue. Please check your internet connection.\", comment: \"\")\n    case .permissionDenied:\n      return NSLocalizedString(\"You don't have permission to access this data.\", comment: \"\")\n    case .documentNotFound:\n      return NSLocalizedString(\"The requested data could not be found.\", comment: \"\")\n    case .serializationError:\n      return NSLocalizedString(\"There was a problem processing the data.\", comment: \"\")\n    case .unknownError(let error):\n      return NSLocalizedString(\"An unexpected error occurred: \\(error.localizedDescription)\", comment: \"\")\n    }\n  }\n}\n```\n2. Implement error mapping in UserProfileService:\n```swift\nprivate func mapFirestoreError(_ error: Error) -> FirestoreError {\n  if let nsError = error as NSError? {\n    switch nsError.domain {\n    case FirestoreErrorDomain:\n      switch FirestoreErrorCode(rawValue: nsError.code) {\n      case .notFound:\n        return .documentNotFound\n      case .permissionDenied:\n        return .permissionDenied\n      case .unavailable, .networkError:\n        return .networkError\n      default:\n        return .unknownError(error)\n      }\n    default:\n      return .unknownError(error)\n    }\n  }\n  return .unknownError(error)\n}\n```\n3. Implement retry mechanism for transient errors:\n```swift\nfunc fetchPreferencesWithRetry(for userId: String, maxRetries: Int = 3) async throws -> UserPreferences {\n  var retryCount = 0\n  var lastError: Error? = nil\n  \n  while retryCount < maxRetries {\n    do {\n      return try await fetchPreferences(for: userId)\n    } catch let error as NSError {\n      lastError = error\n      // Only retry for network-related errors\n      if error.domain == FirestoreErrorDomain && \n         (error.code == FirestoreErrorCode.unavailable.rawValue || \n          error.code == FirestoreErrorCode.networkError.rawValue) {\n        retryCount += 1\n        if retryCount < maxRetries {\n          // Exponential backoff\n          let delaySeconds = pow(Double(2), Double(retryCount))\n          try await Task.sleep(nanoseconds: UInt64(delaySeconds * 1_000_000_000))\n          continue\n        }\n      } else {\n        // Non-retryable error\n        throw mapFirestoreError(error)\n      }\n    }\n  }\n  \n  throw mapFirestoreError(lastError ?? NSError(domain: \"UserProfileService\", code: -1, userInfo: nil))\n}\n```\n4. Add logging for all errors with appropriate detail level", "testStrategy": "1. Create unit tests for each error type\n2. Test retry mechanism with mocked network failures\n3. Verify exponential backoff works correctly\n4. Test error mapping from Firestore errors to app-specific errors\n5. Verify user-facing error messages are appropriate\n6. Test integration with UI error handling", "priority": "medium", "dependencies": [17], "status": "pending", "subtasks": []}, {"id": 20, "title": "Integrate UserProfileService with AuthenticationService", "description": "Integrate the UserProfileService with the existing AuthenticationService to automatically fetch or create user preferences upon successful authentication.", "details": "1. Update AuthenticationService to include UserProfileService integration:\n```swift\nclass AuthenticationService {\n  // Existing authentication properties\n  \n  // Add user preferences property\n  @Published var userPreferences: UserPreferences?\n  @Published var preferencesLoadingState: LoadingState = .idle\n  \n  enum LoadingState {\n    case idle\n    case loading\n    case success\n    case error(FirestoreError)\n  }\n  \n  // Update authentication state change handler\n  private func handleAuthStateChange() {\n    if let user = Auth.auth().currentUser {\n      self.isAuthenticated = true\n      self.userId = user.uid\n      // Fetch user preferences\n      loadUserPreferences(for: user.uid)\n    } else {\n      self.isAuthenticated = false\n      self.userId = nil\n      self.userPreferences = nil\n    }\n  }\n  \n  // Add method to load user preferences\n  private func loadUserPreferences(for userId: String) {\n    preferencesLoadingState = .loading\n    \n    Task {\n      do {\n        let preferences = try await UserProfileService.shared.fetchOrCreatePreferences(for: userId)\n        await MainActor.run {\n          self.userPreferences = preferences\n          self.preferencesLoadingState = .success\n        }\n      } catch let error as FirestoreError {\n        await MainActor.run {\n          self.preferencesLoadingState = .error(error)\n        }\n      } catch {\n        await MainActor.run {\n          self.preferencesLoadingState = .error(.unknownError(error))\n        }\n      }\n    }\n  }\n  \n  // Add method to save user preferences\n  func saveUserPreferences(_ preferences: UserPreferences) async throws {\n    guard let userId = userId else {\n      throw FirestoreError.permissionDenied\n    }\n    \n    preferencesLoadingState = .loading\n    \n    do {\n      try await UserProfileService.shared.savePreferences(preferences, for: userId)\n      await MainActor.run {\n        self.userPreferences = preferences\n        self.preferencesLoadingState = .success\n      }\n    } catch {\n      let mappedError = (error as? FirestoreError) ?? FirestoreError.unknownError(error)\n      await MainActor.run {\n        self.preferencesLoadingState = .error(mappedError)\n      }\n      throw mappedError\n    }\n  }\n}\n```\n2. Update sign-in methods to handle user preferences:\n```swift\nfunc signInWithApple(credential: ASAuthorizationAppleIDCredential) async throws {\n  // Existing sign-in code\n  \n  // After successful sign-in\n  if let user = Auth.auth().currentUser {\n    loadUserPreferences(for: user.uid)\n  }\n}\n\n// Similar updates for other sign-in methods\n```\n3. Ensure proper error handling and loading states are propagated to the UI", "testStrategy": "1. Create unit tests for the integration between AuthenticationService and UserProfileService\n2. Test the full authentication flow including preferences loading\n3. Test error handling during preferences loading\n4. Verify loading states are correctly updated\n5. Test with both new and existing users\n6. Verify preferences are correctly loaded after authentication\n7. Test sign-out clears preferences data", "priority": "high", "dependencies": [17, 19], "status": "pending", "subtasks": [{"id": 1, "title": "Update AuthenticationService with UserPreferences Properties", "description": "Add UserPreferences properties and loading state enum to the AuthenticationService class to track user preferences data and loading status.", "dependencies": [], "details": "Add the following to AuthenticationService:\n- @Published var userPreferences: UserPreferences? property\n- @Published var preferencesLoadingState: LoadingState = .idle\n- Implement LoadingState enum with idle, loading, success, and error cases\n- Ensure proper property initialization in the class constructor", "status": "pending", "testStrategy": "Test that the properties are correctly initialized and that the LoadingState enum works as expected with all states. Verify published properties trigger UI updates when changed."}, {"id": 2, "title": "Implement handleAuthStateChange Method Update", "description": "Update the handleAuthStateChange method to fetch user preferences when a user is authenticated and reset preferences when logged out.", "dependencies": ["20.1"], "details": "Modify the existing handleAuthStateChange method to:\n- Call loadUserPreferences when a user is authenticated\n- Reset userPreferences to nil when logged out\n- Pass the user.uid to the loadUserPreferences method\n- Ensure state transitions are handled properly", "status": "pending", "testStrategy": "Test authentication state changes trigger the correct preference loading behavior. Verify preferences are cleared on logout and loaded on login. Test with both new and existing users."}, {"id": 3, "title": "Implement loadUserPreferences Method", "description": "Create the loadUserPreferences method that calls UserProfileService to fetch or create user preferences and handles loading states and errors.", "dependencies": ["20.1", "20.2"], "details": "Implement the loadUserPreferences method exactly as specified in the Firestore_Implementation_Directive.md document section 2.3:\n- Set loading state before fetching\n- Use Task for asynchronous operation\n- Call UserProfileService.shared.fetchOrCreatePreferences\n- Update UI on the MainActor\n- Handle both FirestoreError and generic errors\n- Update loading state based on result", "status": "pending", "testStrategy": "Test successful preference loading, error handling for various error types, and proper MainActor dispatching. Verify loading states are correctly updated throughout the process."}, {"id": 4, "title": "Implement saveUserPreferences Method", "description": "Create the saveUserPreferences method that saves updated user preferences to Firestore via UserProfileService and handles loading states and errors.", "dependencies": ["20.1", "20.3"], "details": "Implement the saveUserPreferences method that:\n- Validates user is authenticated before saving\n- Sets loading state to .loading\n- Calls UserProfileService.shared.savePreferences\n- Updates local userPreferences on success\n- Properly maps and propagates errors\n- Updates loading state based on result\n- Uses MainActor for UI updates", "status": "pending", "testStrategy": "Test saving preferences with valid and invalid data, error handling, and state management. Verify preferences are correctly updated locally after successful save operations."}, {"id": 5, "title": "Update Sign-in Methods for User Preferences", "description": "Update all authentication methods (Apple, Email, etc.) to load user preferences after successful authentication.", "dependencies": ["20.3", "20.4"], "details": "Modify all sign-in methods to:\n- Call loadUserPreferences after successful authentication\n- Pass the correct user ID to the method\n- Handle potential errors during preference loading\n- Ensure sign-in methods for Apple, Email, and other providers are updated consistently\n- Update sign-up methods to create default preferences for new users", "status": "pending", "testStrategy": "Test the complete authentication flow including preferences loading for all sign-in methods. Verify new users get default preferences and existing users retrieve their saved preferences."}]}, {"id": 21, "title": "Implement UI Loading and Sync Status Indicators", "description": "Create UI components to provide visual feedback for loading, syncing, and error states during user preferences operations.", "details": "1. Create a reusable SyncStatusView component:\n```swift\nstruct SyncStatusView: View {\n  var state: AuthenticationService.LoadingState\n  \n  var body: some View {\n    switch state {\n    case .idle:\n      EmptyView()\n    case .loading:\n      ProgressView()\n        .progressViewStyle(CircularProgressViewStyle())\n    case .success:\n      Image(systemName: \"checkmark.circle.fill\")\n        .foregroundColor(.green)\n        .transition(.opacity)\n        .onAppear {\n          DispatchQueue.main.asyncAfter(deadline: .now() + 2) {\n            withAnimation {\n              // Handle auto-hiding if needed\n            }\n          }\n        }\n    case .error(let error):\n      HStack {\n        Image(systemName: \"exclamationmark.triangle.fill\")\n          .foregroundColor(.red)\n        Text(error.localizedDescription)\n          .font(.caption)\n          .foregroundColor(.red)\n      }\n    }\n  }\n}\n```\n2. Create a NetworkStatusBanner for offline mode:\n```swift\nstruct NetworkStatusBanner: View {\n  @ObservedObject var networkMonitor = NetworkMonitor.shared\n  \n  var body: some View {\n    if !networkMonitor.isConnected {\n      VStack {\n        HStack {\n          Image(systemName: \"wifi.slash\")\n          Text(\"You're offline. Changes will sync when connection is restored.\")\n            .font(.caption)\n          Spacer()\n        }\n        .padding(8)\n        .background(Color.orange.opacity(0.2))\n        .foregroundColor(.orange)\n        .cornerRadius(8)\n        \n        Spacer()\n      }\n      .padding(.horizontal)\n      .transition(.move(edge: .top).combined(with: .opacity))\n    } else {\n      EmptyView()\n    }\n  }\n}\n\n// Simple network monitor class\nclass NetworkMonitor: ObservableObject {\n  static let shared = NetworkMonitor()\n  private let monitor = NWPathMonitor()\n  private let queue = DispatchQueue(label: \"NetworkMonitor\")\n  \n  @Published var isConnected = true\n  \n  private init() {\n    monitor.pathUpdateHandler = { [weak self] path in\n      DispatchQueue.main.async {\n        self?.isConnected = path.status == .satisfied\n      }\n    }\n    monitor.start(queue: queue)\n  }\n  \n  deinit {\n    monitor.cancel()\n  }\n}\n```\n3. Integrate these components into PreferencesEditView:\n```swift\nstruct PreferencesEditView: View {\n  @EnvironmentObject var authService: AuthenticationService\n  @State private var preferences: UserPreferences\n  @State private var isSaving = false\n  \n  var body: some View {\n    Form {\n      // Preference editing fields\n      \n      Section {\n        Button(\"Save Changes\") {\n          savePreferences()\n        }\n        .disabled(isSaving)\n      }\n      \n      // Status indicator section\n      Section {\n        SyncStatusView(state: authService.preferencesLoadingState)\n      }\n    }\n    .overlay(NetworkStatusBanner())\n    // Add alert for errors\n    .alert(isPresented: $showError) {\n      Alert(title: Text(\"Error\"), message: Text(errorMessage), dismissButton: .default(Text(\"OK\")))\n    }\n  }\n  \n  private func savePreferences() {\n    isSaving = true\n    Task {\n      do {\n        try await authService.saveUserPreferences(preferences)\n        await MainActor.run {\n          isSaving = false\n        }\n      } catch {\n        await MainActor.run {\n          isSaving = false\n          showError = true\n          errorMessage = error.localizedDescription\n        }\n      }\n    }\n  }\n}\n```", "testStrategy": "1. Create UI tests for loading and sync status indicators\n2. Test appearance and disappearance of status indicators\n3. Test offline banner appears when network is disconnected\n4. Verify error messages are displayed correctly\n5. Test with different network conditions\n6. Verify accessibility of all status indicators\n7. Test animation transitions", "priority": "medium", "dependencies": [20], "status": "pending", "subtasks": []}, {"id": 22, "title": "Implement Offline Mode Support", "description": "Leverage Firebase Firestore's offline persistence capabilities to ensure the app works seamlessly in offline mode with automatic synchronization when connectivity is restored.", "details": "1. Configure Firestore persistence settings (optional, as it's enabled by default on iOS):\n```swift\n// In AppDelegate or during app initialization\nlet settings = FirestoreSettings()\nsettings.isPersistenceEnabled = true\nsettings.cacheSizeBytes = FirestoreCacheSizeUnlimited\nFirestore.firestore().settings = settings\n```\n2. Add network state monitoring to provide user feedback:\n```swift\n// Use the NetworkMonitor class from Task 21\n```\n3. Implement optimistic UI updates:\n```swift\n// In PreferencesEditView\nprivate func savePreferences() {\n  // Immediately update local state\n  let updatedPreferences = self.preferences\n  authService.userPreferences = updatedPreferences\n  \n  // Then save to Firestore\n  Task {\n    do {\n      try await authService.saveUserPreferences(updatedPreferences)\n    } catch {\n      // On error, show error message but don't revert UI\n      // as Firestore will retry automatically when online\n      await MainActor.run {\n        showError = true\n        errorMessage = error.localizedDescription\n      }\n    }\n  }\n}\n```\n4. Add offline indicator in UI when changes are pending sync:\n```swift\nstruct SyncPendingIndicator: View {\n  @ObservedObject var networkMonitor = NetworkMonitor.shared\n  @ObservedObject var authService: AuthenticationService\n  \n  var body: some View {\n    if !networkMonitor.isConnected && authService.preferencesLoadingState == .loading {\n      HStack {\n        Image(systemName: \"clock.arrow.circlepath\")\n        Text(\"Changes will sync when online\")\n          .font(.caption)\n        Spacer()\n      }\n      .padding(8)\n      .background(Color.yellow.opacity(0.2))\n      .foregroundColor(.orange)\n      .cornerRadius(8)\n    } else {\n      EmptyView()\n    }\n  }\n}\n```\n5. Document offline behavior for users in help section", "testStrategy": "1. Test app behavior when network connection is lost during operation\n2. Verify changes made offline are synchronized when connection is restored\n3. Test UI indicators for offline mode\n4. Verify optimistic UI updates work correctly\n5. Test with airplane mode toggling\n6. Verify data consistency after offline-online transitions\n7. Test with large preference changes to ensure cache works correctly", "priority": "medium", "dependencies": [17, 21], "status": "pending", "subtasks": []}, {"id": 23, "title": "Implement Default User Preferences Creation", "description": "Create a system to automatically initialize default preferences for new users upon first login, ensuring all users have a complete configuration foundation.", "details": "1. Define default preferences in UserPreferences model:\n```swift\nextension UserPreferences {\n  static func createDefault(for userId: String) -> UserPreferences {\n    return UserPreferences(\n      userId: userId,\n      allergies: [],\n      dietaryRestrictions: [],\n      familySize: 1,\n      // Set all other properties to sensible defaults\n      createdAt: Date(),\n      updatedAt: Date()\n    )\n  }\n}\n```\n2. Enhance UserProfileService to handle new user detection:\n```swift\nfunc fetchOrCreatePreferences(for userId: String) async throws -> UserPreferences {\n  do {\n    // Try to fetch existing preferences\n    return try await fetchPreferences(for: userId)\n  } catch FirestoreError.documentNotFound {\n    // User document doesn't exist, create default preferences\n    let defaultPreferences = UserPreferences.createDefault(for: userId)\n    try await savePreferences(defaultPreferences, for: userId)\n    return defaultPreferences\n  } catch {\n    // Rethrow other errors\n    throw error\n  }\n}\n```\n3. Add analytics tracking for new user creation:\n```swift\nprivate func trackNewUserCreation(userId: String) {\n  Analytics.logEvent(\"new_user_preferences_created\", parameters: [\n    \"user_id\": userId,\n    \"timestamp\": FieldValue.serverTimestamp()\n  ])\n}\n```\n4. Add first-time user onboarding guidance:\n```swift\nstruct FirstTimePreferencesSetupView: View {\n  @EnvironmentObject var authService: AuthenticationService\n  @State private var preferences: UserPreferences\n  @State private var currentStep = 0\n  \n  var body: some View {\n    VStack {\n      // Step indicator\n      StepIndicator(currentStep: currentStep, totalSteps: 3)\n      \n      // Content based on current step\n      switch currentStep {\n      case 0:\n        AllergiesSetupView(allergies: $preferences.allergies)\n      case 1:\n        DietaryRestrictionsView(restrictions: $preferences.dietaryRestrictions)\n      case 2:\n        FamilySizeSetupView(familySize: $preferences.familySize)\n      default:\n        EmptyView()\n      }\n      \n      // Navigation buttons\n      HStack {\n        if currentStep > 0 {\n          Button(\"Back\") {\n            currentStep -= 1\n          }\n        }\n        \n        Spacer()\n        \n        Button(currentStep == 2 ? \"Finish\" : \"Next\") {\n          if currentStep < 2 {\n            currentStep += 1\n          } else {\n            // Save final preferences\n            saveAndFinish()\n          }\n        }\n      }\n    }\n    .padding()\n  }\n  \n  private func saveAndFinish() {\n    Task {\n      try await authService.saveUserPreferences(preferences)\n      // Navigate to main app\n    }\n  }\n}\n```", "testStrategy": "1. Test default preferences creation for new users\n2. Verify all default values are appropriate and complete\n3. Test the first-time setup flow\n4. Verify analytics events are triggered correctly\n5. Test with various user scenarios (empty values, maximum values)\n6. Verify onboarding UI works on different device sizes\n7. Test navigation between setup steps", "priority": "high", "dependencies": [17, 20], "status": "pending", "subtasks": []}, {"id": 24, "title": "Implement Preferences Edit View Integration", "description": "Integrate the UserProfileService with the PreferencesEditView to enable saving and syncing of user preferences changes to Firestore.", "details": "1. Update PreferencesEditView to use UserProfileService:\n```swift\nstruct PreferencesEditView: View {\n  @EnvironmentObject var authService: AuthenticationService\n  @State private var preferences: UserPreferences\n  @State private var isEdited = false\n  @State private var showSaveConfirmation = false\n  \n  init(preferences: UserPreferences) {\n    _preferences = State(initialValue: preferences)\n  }\n  \n  var body: some View {\n    Form {\n      // Allergies Section\n      Section(header: Text(\"Allergies\")) {\n        AllergiesEditView(allergies: $preferences.allergies)\n          .onChange(of: preferences.allergies) { _ in isEdited = true }\n      }\n      \n      // Dietary Restrictions Section\n      Section(header: Text(\"Dietary Restrictions\")) {\n        DietaryRestrictionsView(restrictions: $preferences.dietaryRestrictions)\n          .onChange(of: preferences.dietaryRestrictions) { _ in isEdited = true }\n      }\n      \n      // Family Size Section\n      Section(header: Text(\"Family Size\")) {\n        Stepper(\"\\(preferences.familySize) \\(preferences.familySize == 1 ? \"person\" : \"people\")\", \n                value: $preferences.familySize, in: 1...10)\n          .onChange(of: preferences.familySize) { _ in isEdited = true }\n      }\n      \n      // Other preference sections...\n      \n      // Save Button Section\n      Section {\n        Button(action: savePreferences) {\n          HStack {\n            Spacer()\n            Text(\"Save Changes\")\n            Spacer()\n          }\n        }\n        .disabled(!isEdited)\n        .buttonStyle(BorderedButtonStyle())\n      }\n    }\n    .navigationTitle(\"Preferences\")\n    .overlay(Group {\n      if authService.preferencesLoadingState == .loading {\n        ProgressView()\n          .progressViewStyle(CircularProgressViewStyle())\n          .scaleEffect(1.5)\n          .frame(maxWidth: .infinity, maxHeight: .infinity)\n          .background(Color.black.opacity(0.1))\n      }\n    })\n    .overlay(NetworkStatusBanner())\n    .alert(isPresented: $showSaveConfirmation) {\n      Alert(\n        title: Text(\"Changes Saved\"),\n        message: Text(\"Your preferences have been updated.\"),\n        dismissButton: .default(Text(\"OK\"))\n      )\n    }\n  }\n  \n  private func savePreferences() {\n    // Update timestamp\n    preferences.updatedAt = Date()\n    \n    Task {\n      do {\n        try await authService.saveUserPreferences(preferences)\n        await MainActor.run {\n          isEdited = false\n          showSaveConfirmation = true\n        }\n      } catch {\n        // Handle error\n      }\n    }\n  }\n}\n```\n2. Create specialized edit components for different preference types:\n```swift\nstruct AllergiesEditView: View {\n  @Binding var allergies: [String]\n  @State private var newAllergy = \"\"\n  \n  var body: some View {\n    VStack {\n      // Display existing allergies\n      ForEach(allergies, id: \\.self) { allergy in\n        HStack {\n          Text(allergy)\n          Spacer()\n          Button(action: { removeAllergy(allergy) }) {\n            Image(systemName: \"minus.circle.fill\")\n              .foregroundColor(.red)\n          }\n        }\n      }\n      \n      // Add new allergy\n      HStack {\n        TextField(\"Add allergy\", text: $newAllergy)\n        Button(action: addAllergy) {\n          Image(systemName: \"plus.circle.fill\")\n            .foregroundColor(.green)\n        }\n        .disabled(newAllergy.isEmpty)\n      }\n    }\n  }\n  \n  private func addAllergy() {\n    let trimmed = newAllergy.trimmingCharacters(in: .whitespacesAndNewlines)\n    if !trimmed.isEmpty && !allergies.contains(trimmed) {\n      allergies.append(trimmed)\n      newAllergy = \"\"\n    }\n  }\n  \n  private func removeAllergy(_ allergy: String) {\n    allergies.removeAll { $0 == allergy }\n  }\n}\n```\n3. Add confirmation for discarding changes:\n```swift\n.navigationBarBackButtonHidden(isEdited)\n.toolbar {\n  ToolbarItem(placement: .navigationBarLeading) {\n    if isEdited {\n      Button(\"Back\") {\n        showDiscardAlert = true\n      }\n    }\n  }\n}\n.alert(isPresented: $showDiscardAlert) {\n  Alert(\n    title: Text(\"Discard Changes?\"),\n    message: Text(\"You have unsaved changes. Are you sure you want to discard them?\"),\n    primaryButton: .destructive(Text(\"Discard\")) {\n      // Navigate back\n      presentationMode.wrappedValue.dismiss()\n    },\n    secondaryButton: .cancel()\n  )\n}\n```", "testStrategy": "1. Test the complete edit-save flow for user preferences\n2. Verify changes are correctly saved to Firestore\n3. Test UI feedback during save operations\n4. Verify discard changes confirmation works\n5. Test with various input types and edge cases\n6. Verify form validation works correctly\n7. Test accessibility of all form elements", "priority": "high", "dependencies": [20, 21, 22], "status": "pending", "subtasks": []}, {"id": 25, "title": "Implement Cross-Device Sync Testing", "description": "Create a comprehensive testing framework to verify user preferences synchronize correctly across multiple devices and handle conflict resolution appropriately.", "details": "1. Create a test plan document:\n```markdown\n# Cross-Device Sync Testing Plan\n\n## Test Environments\n- iPhone (iOS 17.0+)\n- iPad (iOS 17.0+)\n- Multiple simulators running simultaneously\n\n## Test Scenarios\n\n### Basic Sync Tests\n1. **New User Setup**\n   - Create new user on Device A\n   - Verify default preferences\n   - Login with same user on Device B\n   - Verify preferences match\n\n2. **Preference Modification**\n   - Modify preferences on Device A\n   - Verify changes appear on Device B\n   - Measure sync time\n\n### Conflict Resolution Tests\n1. **Simultaneous Edits**\n   - Put Device B in airplane mode\n   - Edit preferences on Device A and save\n   - Edit same preferences differently on Device B\n   - Turn off airplane mode on Device B\n   - Verify last-write-wins behavior\n\n2. **Offline Changes**\n   - Put Device A in airplane mode\n   - Make multiple changes on Device A\n   - Verify changes sync when connection restored\n\n### Edge Cases\n1. **Large Preference Sets**\n   - Create very large preference sets (100+ allergies)\n   - Test sync performance\n\n2. **Rapid Changes**\n   - Make rapid sequential changes\n   - Verify all changes are captured\n```\n2. Implement test helper for simulating network conditions:\n```swift\nclass NetworkConditionSimulator {\n  static func simulateOfflineMode() {\n    let url = URL(string: \"http://localhost:8080\")!\n    URLProtocol.registerClass(MockURLProtocol.self)\n    let configuration = URLSessionConfiguration.ephemeral\n    configuration.protocolClasses = [MockURLProtocol.self]\n    // Configure to simulate offline\n    MockURLProtocol.requestHandler = { request in\n      throw NSError(domain: NSURLErrorDomain, code: NSURLErrorNotConnectedToInternet, userInfo: nil)\n    }\n  }\n  \n  static func simulateSlowNetwork() {\n    // Similar implementation with delay\n  }\n  \n  static func resetNetworkConditions() {\n    URLProtocol.unregisterClass(MockURLProtocol.self)\n  }\n}\n```\n3. Create automated UI tests for cross-device scenarios:\n```swift\nfunc testCrossDeviceSync() {\n  // Setup two simulator instances\n  let deviceA = XCUIApplication(bundleIdentifier: \"com.yourapp.IngredientScanner\")\n  let deviceB = XCUIApplication(bundleIdentifier: \"com.yourapp.IngredientScanner\")\n  \n  // Launch both apps\n  deviceA.launch()\n  deviceB.launch()\n  \n  // Login with same account on both devices\n  loginToApp(app: deviceA, email: \"<EMAIL>\", password: \"password\")\n  loginToApp(app: deviceB, email: \"<EMAIL>\", password: \"password\")\n  \n  // Make changes on device A\n  navigateToPreferences(app: deviceA)\n  addAllergy(app: deviceA, allergyName: \"Peanuts\")\n  savePreferences(app: deviceA)\n  \n  // Verify changes appear on device B (with reasonable wait)\n  let expectation = XCTestExpectation(description: \"Sync completes\")\n  DispatchQueue.main.asyncAfter(deadline: .now() + 5) {\n    navigateToPreferences(app: deviceB)\n    XCTAssertTrue(deviceB.staticTexts[\"Peanuts\"].exists)\n    expectation.fulfill()\n  }\n  \n  wait(for: [expectation], timeout: 10)\n}\n```\n4. Document test results and sync behavior", "testStrategy": "1. Execute the cross-device test plan manually\n2. Automate key test scenarios using XCTest\n3. Test with various network conditions\n4. Document sync latency under different conditions\n5. Test with real devices in addition to simulators\n6. Verify conflict resolution behavior\n7. Create regression test suite for future updates", "priority": "medium", "dependencies": [22, 24], "status": "pending", "subtasks": []}, {"id": 26, "title": "Implement Analytics for Sync Operations", "description": "Add analytics tracking for synchronization operations to monitor performance, error rates, and user behavior patterns related to preference management.", "details": "1. Define key events to track:\n```swift\nenum SyncAnalyticsEvent: String {\n  case syncStarted = \"preferences_sync_started\"\n  case syncCompleted = \"preferences_sync_completed\"\n  case syncFailed = \"preferences_sync_failed\"\n  case preferencesCreated = \"preferences_created\"\n  case preferencesUpdated = \"preferences_updated\"\n  case offlineModeEntered = \"offline_mode_entered\"\n  case offlineModeExited = \"offline_mode_exited\"\n}\n```\n2. Create an analytics service:\n```swift\nclass AnalyticsService {\n  static let shared = AnalyticsService()\n  \n  private init() {}\n  \n  func logEvent(_ event: SyncAnalyticsEvent, parameters: [String: Any]? = nil) {\n    var eventParams = parameters ?? [String: Any]()\n    // Add common parameters\n    eventParams[\"timestamp\"] = FieldValue.serverTimestamp()\n    eventParams[\"app_version\"] = Bundle.main.infoDictionary?[\"CFBundleShortVersionString\"] as? String\n    \n    // Log to Firebase Analytics\n    Analytics.logEvent(event.rawValue, parameters: eventParams)\n  }\n  \n  func logSyncDuration(durationMs: Int) {\n    logEvent(.syncCompleted, parameters: [\"duration_ms\": durationMs])\n  }\n  \n  func logSyncError(_ error: Error) {\n    logEvent(.syncFailed, parameters: [\n      \"error_code\": (error as NSError).code,\n      \"error_domain\": (error as NSError).domain,\n      \"error_description\": error.localizedDescription\n    ])\n  }\n}\n```\n3. Integrate analytics in UserProfileService:\n```swift\nfunc savePreferences(_ preferences: UserPreferences, for userId: String) async throws {\n  let startTime = Date()\n  AnalyticsService.shared.logEvent(.syncStarted)\n  \n  do {\n    let docRef = userDocument(for: userId)\n    try docRef.setData(from: preferences)\n    \n    let duration = Int(Date().timeIntervalSince(startTime) * 1000)\n    AnalyticsService.shared.logSyncDuration(durationMs: duration)\n    \n    // Track which preferences were updated\n    AnalyticsService.shared.logEvent(.preferencesUpdated, parameters: [\n      \"allergies_count\": preferences.allergies.count,\n      \"restrictions_count\": preferences.dietaryRestrictions.count,\n      \"family_size\": preferences.familySize\n    ])\n  } catch {\n    AnalyticsService.shared.logSyncError(error)\n    throw error\n  }\n}\n```\n4. Track network state changes:\n```swift\n// In NetworkMonitor\nprivate var previousConnectionState = true\n\nprivate func updateConnectionState(isConnected: Bool) {\n  if previousConnectionState != isConnected {\n    if isConnected {\n      AnalyticsService.shared.logEvent(.offlineModeExited)\n    } else {\n      AnalyticsService.shared.logEvent(.offlineModeEntered)\n    }\n    previousConnectionState = isConnected\n  }\n  \n  DispatchQueue.main.async {\n    self.isConnected = isConnected\n  }\n}\n```\n5. Create a dashboard for monitoring sync performance:\n```markdown\n# Sync Analytics Dashboard\n\n## Key Metrics to Monitor\n1. Average sync duration\n2. Sync failure rate\n3. Offline mode frequency\n4. Most commonly updated preferences\n5. User engagement with preferences\n\n## Firebase Analytics Queries\n- Query for average sync duration:\n  ```\n  SELECT AVG(param.duration_ms)\n  FROM firebase_analytics.events\n  WHERE event_name = 'preferences_sync_completed'\n  ```\n\n- Query for sync failure rate:\n  ```\n  SELECT COUNT(*) / (\n    SELECT COUNT(*) FROM firebase_analytics.events\n    WHERE event_name IN ('preferences_sync_completed', 'preferences_sync_failed')\n  ) as failure_rate\n  FROM firebase_analytics.events\n  WHERE event_name = 'preferences_sync_failed'\n  ```\n```", "testStrategy": "1. Verify all analytics events are correctly logged\n2. Test with different network conditions\n3. Validate parameter values are correct\n4. Check Firebase Analytics dashboard for event reception\n5. Test with high volume of sync operations\n6. Verify offline mode events are tracked correctly\n7. Create test queries to validate data collection", "priority": "low", "dependencies": [17, 22, 24], "status": "pending", "subtasks": []}, {"id": 27, "title": "Create Documentation and Developer Guide", "description": "Create comprehensive documentation for the user preferences synchronization system, including architecture overview, implementation details, and developer guidelines.", "details": "1. Create a README.md file:\n```markdown\n# User Preferences Sync System\n\n## Overview\nThis system provides seamless synchronization of user preferences across multiple devices using Firebase Firestore. It supports offline operations, conflict resolution, and automatic user preference initialization.\n\n## Architecture\n\n### Core Components\n- **UserProfileService**: Singleton service for Firestore interactions\n- **UserPreferences**: Data model for user settings\n- **AuthenticationService**: Handles user authentication and preferences loading\n- **NetworkMonitor**: Tracks network connectivity status\n\n### Data Flow\n1. User authenticates via AuthenticationService\n2. UserProfileService fetches or creates user preferences\n3. UI components display and allow editing of preferences\n4. Changes are saved to Firestore and propagated to other devices\n\n## Implementation Details\n\n### Firestore Structure\n- Collection: `users`\n- Document ID: User's Firebase Auth UID\n- Document contains full UserPreferences object\n\n### Offline Support\nThe system leverages Firestore's built-in offline persistence capabilities:\n- Changes made offline are queued for sync\n- UI provides feedback on sync status\n- Conflict resolution uses last-write-wins strategy\n\n## Usage Guide\n\n### Fetching User Preferences\n```swift\nlet preferences = try await UserProfileService.shared.fetchPreferences(for: userId)\n```\n\n### Saving User Preferences\n```swift\ntry await UserProfileService.shared.savePreferences(preferences, for: userId)\n```\n\n### Handling Loading States\n```swift\nswitch authService.preferencesLoadingState {\ncase .loading:\n  // Show loading indicator\ncase .success:\n  // Show success confirmation\ncase .error(let error):\n  // Show error message\ncase .idle:\n  // Default state\n}\n```\n\n## Security Considerations\n- Firestore security rules ensure users can only access their own data\n- All network requests use HTTPS\n- No sensitive data is stored in preferences\n```\n2. Create API documentation with DocC:\n```swift\n/// A service that manages user preference synchronization with Firestore.\n///\n/// UserProfileService provides methods to save and retrieve user preferences\n/// from Firebase Firestore. It handles offline persistence, error management,\n/// and new user initialization.\n///\n/// - Important: This service requires a configured Firebase instance.\n///\n/// ## Topics\n/// ### Retrieving Preferences\n/// - ``fetchPreferences(for:)``\n/// - ``fetchOrCreatePreferences(for:)``\n///\n/// ### Saving Preferences\n/// - ``savePreferences(_:for:)``\n///\n/// ### Error Handling\n/// - ``UserProfileError``\npublic class UserProfileService {\n    // Implementation\n}\n```\n3. Create architecture diagrams:\n```\n+-------------------+      +-------------------+\n| AuthenticationSvc |----->| UserProfileSvc    |\n+-------------------+      +-------------------+\n         |                         |\n         v                         v\n+-------------------+      +-------------------+\n| UI Components     |<---->| Firestore         |\n+-------------------+      +-------------------+\n         |                         ^\n         v                         |\n+-------------------+              |\n| NetworkMonitor    |--------------+\n+-------------------+\n```\n4. Document testing procedures:\n```markdown\n## Testing Guide\n\n### Unit Testing\n1. Run `UserProfileServiceTests` to verify core functionality\n2. Run `OfflineSyncTests` to verify offline behavior\n\n### Integration Testing\n1. Run `AuthIntegrationTests` to verify auth flow\n2. Run `PreferencesUITests` to verify UI integration\n\n### Cross-Device Testing\n1. Follow the cross-device test plan in `TestPlans/CrossDeviceSync.md`\n2. Use Firebase Test Lab for multi-device testing\n```", "testStrategy": "1. Review documentation for accuracy and completeness\n2. Verify code examples work as documented\n3. Have another developer follow the documentation to implement a feature\n4. Test API documentation with DocC preview\n5. Verify architecture diagrams match implementation\n6. Check for missing edge cases or scenarios\n7. Ensure security considerations are adequately documented", "priority": "low", "dependencies": [16, 17, 18, 20, 24, 25], "status": "pending", "subtasks": []}], "metadata": {"created": "2025-08-08T06:08:29.802Z", "updated": "2025-08-08T06:27:06.192Z", "description": "Tasks for master context"}}}