import Foundation

// MARK: - Data Structures

/// Represents an update operation for an existing ingredient
struct UpdateOp: Codable {
    let id: UUID
    let newName: String
    let newCategory: String
}

/// Represents a merge operation to consolidate duplicates
struct MergeOp: Codable {
    let winnerId: UUID
    let loserIds: [UUID]
}

/// Complete plan for cleaning up the pantry
struct CleanUpPlan: Codable {
    var updates: [UpdateOp]
    var removals: [UUID]
    var merges: [MergeOp]
}

/// Summary of organization results
struct PantryOrganizerSummary {
    var updatedCount: Int
    var removedCount: Int
    var mergedCount: Int
}

/// Errors that can occur during pantry organization
enum OrganizerError: Error, LocalizedError {
    case invalidResponse(String)
    case applicationError(String)
    case apiError(String)
    case parseError(String)
    case partialBatchSuccess(processedCount: Int, totalCount: Int)
    
    var errorDescription: String? {
        switch self {
        case .invalidResponse(let message):
            return "Invalid response: \(message)"
        case .applicationError(let message):
            return "Application error: \(message)"
        case .apiError(let message):
            return "API error: \(message)"
        case .parseError(let message):
            return "Parse error: \(message)"
        case .partialBatchSuccess(let processedCount, let totalCount):
            return "Processed \(processedCount) of \(totalCount) items successfully"
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .invalidResponse, .apiError, .parseError:
            return "Please try again later"
        case .applicationError:
            return "Please try again or contact support"
        case .partialBatchSuccess:
            return "Some items couldn't be processed. You can continue with partial results or try again"
        }
    }
}

// MARK: - PantryOrganizerService

actor PantryOrganizerService {
    private let geminiService: GeminiAPIService
    private let pantryService: PantryService
    private let batchSize = 150 // Process 100-200 items per batch
    
    init(geminiService: GeminiAPIService, pantryService: PantryService) {
        self.geminiService = geminiService
        self.pantryService = pantryService
    }
    
    /// Organize pantry items by processing them in batches
    func organize(items: [Ingredient], allowedCategories: [String]) async throws -> CleanUpPlan {
        // Validate inputs
        guard !items.isEmpty else {
            throw OrganizerError.invalidResponse("No items to organize")
        }
        
        guard !allowedCategories.isEmpty else {
            throw OrganizerError.invalidResponse("No allowed categories provided")
        }
        
        // Process in batches to respect token limits
        var fullPlan = CleanUpPlan(updates: [], removals: [], merges: [])
        var processedCount = 0
        var failedBatches = 0
        
        // Split items into batches
        let batches = stride(from: 0, to: items.count, by: batchSize).map {
            Array(items[$0..<min($0 + batchSize, items.count)])
        }
        
        // Process each batch
        for batch in batches {
            do {
                let batchPlan = try await processBatch(items: batch, allowedCategories: allowedCategories)
                fullPlan.updates.append(contentsOf: batchPlan.updates)
                fullPlan.removals.append(contentsOf: batchPlan.removals)
                fullPlan.merges.append(contentsOf: batchPlan.merges)
                processedCount += batch.count
            } catch {
                failedBatches += 1
                print("❌ Failed to process batch: \(error)")
                // Continue with other batches
            }
        }
        
        // Check if we have at least some results
        if processedCount == 0 {
            throw OrganizerError.apiError("Could not process any items")
        }
        
        // Note partial success
        if failedBatches > 0 {
            print("⚠️ Processed \(processedCount) of \(items.count) items successfully")
            throw OrganizerError.partialBatchSuccess(processedCount: processedCount, totalCount: items.count)
        }
        
        return fullPlan
    }
    
    /// Process a single batch of items
    private func processBatch(items: [Ingredient], allowedCategories: [String]) async throws -> CleanUpPlan {
        // Build prompt for this batch
        let prompt = buildOrganizerPrompt(items: items, allowedCategories: allowedCategories)
        
        // Call Gemini API
        let response = try await geminiService.callGeminiAPI(prompt: prompt)
        
        // Parse response into CleanUpPlan
        return try parseCleanUpPlan(response)
    }
    
    /// Build the AI prompt for pantry organization
    private func buildOrganizerPrompt(items: [Ingredient], allowedCategories: [String]) -> String {
        var prompt = """
        You are a pantry organization assistant. Clean up the following pantry items by:
        1. Fixing categories to match exactly one of these allowed categories: \(allowedCategories.joined(separator: ", "))
        2. Cleaning names by removing brand names, sizes, quantities, and marketing text
        3. Preserving meaningful descriptors (e.g., "whole milk" instead of just "milk")
        4. Identifying non-food items or gibberish for removal
        5. Identifying duplicate items that should be merged
        
        For each item, return one of:
        - Update: {"id": "[original id]", "newName": "[cleaned name]", "newCategory": "[exact category]"}
        - Remove: {"id": "[original id]", "remove": true}
        - For duplicates, suggest merges: {"winnerId": "[id to keep]", "loserIds": ["id1", "id2", ...]}
        
        Return ONLY a JSON object with this format: {"updates": [...], "removals": [...], "merges": [...]}
        Do not include any explanation or additional text.
        """
        
        // Add items to prompt
        prompt += "\n\nPANTRY ITEMS:\n"
        for item in items {
            prompt += "{\"id\": \"\(item.id)\", \"name\": \"\(item.name)\", \"category\": \"\(item.category.rawValue)\"}\n"
        }
        
        return prompt
    }
    
    /// Parse the AI response into a structured CleanUpPlan
    private func parseCleanUpPlan(_ response: String) throws -> CleanUpPlan {
        // Extract JSON from response
        guard let jsonData = response.data(using: .utf8) else {
            throw OrganizerError.invalidResponse("Could not convert response to data")
        }
        
        // Decode JSON
        let decoder = JSONDecoder()
        do {
            return try decoder.decode(CleanUpPlan.self, from: jsonData)
        } catch {
            throw OrganizerError.parseError("Failed to decode CleanUpPlan: \(error.localizedDescription)")
        }
    }
    
    /// Apply the cleanup plan to the pantry
    func apply(plan: CleanUpPlan) async throws -> PantryOrganizerSummary {
        var summary = PantryOrganizerSummary(updatedCount: 0, removedCount: 0, mergedCount: 0)
        
        // Apply updates in batches for better performance
        let updateBatches = stride(from: 0, to: plan.updates.count, by: 50).map {
            Array(plan.updates[$0..<min($0 + 50, plan.updates.count)])
        }
        
        for batch in updateBatches {
            do {
                try await applyUpdateBatch(batch)
                summary.updatedCount += batch.count
            } catch {
                print("❌ Error applying update batch: \(error)")
                // Continue with other operations
            }
        }
        
        // Apply removals
        do {
            try await applyRemovals(plan.removals)
            summary.removedCount += plan.removals.count
        } catch {
            print("❌ Error applying removals: \(error)")
        }
        
        // Apply merges one by one (more complex operation)
        for merge in plan.merges {
            do {
                try await applyMerge(merge)
                summary.mergedCount += merge.loserIds.count
            } catch {
                print("❌ Error applying merge: \(error)")
            }
        }
        
        return summary
    }
    
    /// Apply a batch of updates
    private func applyUpdateBatch(_ updates: [UpdateOp]) async throws {
        for update in updates {
            guard let category = PantryCategory(rawValue: update.newCategory) else {
                print("⚠️ Invalid category for update: \(update.newCategory)")
                continue
            }
            
            await pantryService.updateIngredient(
                id: update.id,
                newName: update.newName,
                newCategory: category
            )
        }
    }
    
    /// Apply removals
    private func applyRemovals(_ removals: [UUID]) async throws {
        for id in removals {
            await pantryService.deleteIngredient(id: id)
        }
    }
    
    /// Apply a merge operation
    private func applyMerge(_ merge: MergeOp) async throws {
        // Keep the winner, remove the losers
        for loserId in merge.loserIds {
            await pantryService.deleteIngredient(id: loserId)
        }
        // Note: The winner ingredient remains unchanged
    }
} 