import Foundation
import UIKit

actor GoogleVisionAPIService {
    private let apiKey = APIKeys.googleVisionAPIKey
    private let baseURL = "https://vision.googleapis.com/v1/images:annotate"

    // MARK: - Data Structures
    
    struct VisionResult {
        let detectedText: String
        let detectedLabels: [String]

        var combinedContent: String {
            var content = detectedText
            if !detectedLabels.isEmpty {
                content += "\n\nDetected Items: " + detectedLabels.joined(separator: ", ")
            }
            return content
        }
    }
    
    // New VisionOutput struct for batch processing
    struct VisionOutput: Equatable {
        let ocrText: String
        let labels: [String]
        
        init(ocrText: String, labels: [String]) {
            self.ocrText = ocrText
            self.labels = labels
        }
        
        static func == (lhs: VisionOutput, rhs: VisionOutput) -> Bool {
            return lhs.ocrText == rhs.ocrText && lhs.labels == rhs.labels
        }
    }

    // MARK: - Batch Analysis Methods
    
    /// Process multiple images concurrently with OCR and label detection
    func batchAnalyze(images: [UIImage]) async throws -> [VisionOutput] {
        guard !images.isEmpty else {
            return []
        }
        
        // Resize images if needed to optimize processing
        let processableImages = images.map { resizeImageIfNeeded($0) }
        
        // Process with concurrency limit using chunked approach to avoid concurrent mutation
        let concurrencyLimit = 3 // Cap at 3 concurrent requests
        var results: [VisionOutput] = []
        
        // Process images in chunks to respect concurrency limits
        for startIndex in stride(from: 0, to: processableImages.count, by: concurrencyLimit) {
            let endIndex = min(startIndex + concurrencyLimit, processableImages.count)
            let chunk = Array(processableImages[startIndex..<endIndex])
            
            await withTaskGroup(of: VisionOutput?.self) { group in
                for image in chunk {
                    group.addTask { @Sendable [weak self] in
                        guard let self = self else { return nil }
                        return await self.processImage(image)
                    }
                }
                
                for await result in group {
                    if let validResult = result {
                        results.append(validResult)
                    }
                }
            }
        }
        
        return results
    }
    
    /// Process a single image and return VisionOutput (used internally by batch processing)
    private func processImage(_ image: UIImage) async -> VisionOutput? {
        do {
            let visionResult = try await detectTextAndLabels(in: image)
            return VisionOutput(ocrText: visionResult.detectedText, labels: visionResult.detectedLabels)
        } catch {
            // Return nil for failed operations to allow graceful degradation
            return nil
        }
    }
    
    /// Resize image if its longest dimension exceeds 2000px
    private func resizeImageIfNeeded(_ image: UIImage) -> UIImage {
        let maxDimension: CGFloat = 2000
        let width = image.size.width
        let height = image.size.height
        
        // Check if resizing is needed
        if width <= maxDimension && height <= maxDimension {
            return image
        }
        
        // Calculate new dimensions while maintaining aspect ratio
        let aspectRatio = width / height
        var newWidth: CGFloat
        var newHeight: CGFloat
        
        if width > height {
            newWidth = maxDimension
            newHeight = newWidth / aspectRatio
        } else {
            newHeight = maxDimension
            newWidth = newHeight * aspectRatio
        }
        
        // Resize image
        let size = CGSize(width: newWidth, height: newHeight)
        UIGraphicsBeginImageContextWithOptions(size, false, 1.0)
        defer { UIGraphicsEndImageContext() }
        
        image.draw(in: CGRect(origin: .zero, size: size))
        let resizedImage = UIGraphicsGetImageFromCurrentImageContext() ?? image
        
        return resizedImage
    }

    // MARK: - Individual Analysis Methods (Existing)
    
    func detectTextAndLabels(in image: UIImage) async throws -> VisionResult {
        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
            throw VisionError.invalidImage
        }

        let base64Image = imageData.base64EncodedString()

        let requestBody: [String: Any] = [
            "requests": [
                [
                    "image": ["content": base64Image],
                    "features": [
                        ["type": "TEXT_DETECTION", "maxResults": 10],
                        ["type": "LABEL_DETECTION", "maxResults": 10]
                    ]
                ]
            ]
        ]

        guard let url = URL(string: "\(baseURL)?key=\(apiKey)") else {
            throw VisionError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        } catch {
            throw VisionError.encodingError
        }

        do {
            let (data, response) = try await URLSession.shared.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse,
                  httpResponse.statusCode == 200 else {
                throw VisionError.networkError
            }

            let result = try JSONSerialization.jsonObject(with: data) as? [String: Any]
            guard let responses = result?["responses"] as? [[String: Any]],
                  let firstResponse = responses.first else {
                throw VisionError.invalidResponse
            }

            // Extract text
            var detectedText = ""
            if let textAnnotations = firstResponse["textAnnotations"] as? [[String: Any]],
               let fullText = textAnnotations.first?["description"] as? String {
                detectedText = fullText
            }

            // Extract labels
            var detectedLabels: [String] = []
            if let labelAnnotations = firstResponse["labelAnnotations"] as? [[String: Any]] {
                detectedLabels = labelAnnotations.compactMap { annotation in
                    guard let description = annotation["description"] as? String,
                          let score = annotation["score"] as? Double,
                          score > 0.7 else { 
                        return nil 
                    }
                    return description
                }
            }

            return VisionResult(detectedText: detectedText, detectedLabels: detectedLabels)

        } catch {
            throw VisionError.networkError
        }
    }

    // MARK: - Error Types
    
    enum VisionError: Error, LocalizedError {
        case invalidImage
        case invalidURL
        case encodingError
        case networkError
        case invalidResponse

        var errorDescription: String? {
            switch self {
            case .invalidImage:
                return "Invalid image data"
            case .invalidURL:
                return "Invalid API URL"
            case .encodingError:
                return "Failed to encode request"
            case .networkError:
                return "Network request failed"
            case .invalidResponse:
                return "Invalid API response"
            }
        }
    }
} 