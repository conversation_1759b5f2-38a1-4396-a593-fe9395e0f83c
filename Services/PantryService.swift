import Foundation
import SwiftUI

@Observable
@MainActor
class PantryService {
    var pantryItems: [Ingredient] = []
    var recentlyAddedItems: Set<UUID> = []
    // Removed: private let nameNormalizer = IngredientNameNormalizer()
    
    private let storageService = SwiftDataStorageService.shared

    init() {
        // Load pantry items from SwiftData on initialization
        Task {
            await loadPantryItems()
        }
    }

    func addIngredients(_ newIngredients: [Ingredient]) async {
        // Add all ingredients, allowing duplicates (no normalization)
        var itemsToAdd: [Ingredient] = []
        
        for ingredient in newIngredients {
            // No normalization - store ingredient name as-is
            var newItem = ingredient
            newItem.dateAdded = Date()
            pantryItems.append(newItem)
            itemsToAdd.append(newItem)
        }
        
        // Save to SwiftData storage
        if !itemsToAdd.isEmpty {
            do {
                try await storageService.saveIngredients(itemsToAdd)
            } catch {
                print("❌ Failed to save ingredients to storage: \(error)")
            }
        }
    }

    func markAsRecentlyAdded(_ ingredients: [Ingredient]) {
        // Clear previous recently added items
        recentlyAddedItems.removeAll()
        // Mark new items as recently added
        for ingredient in ingredients {
            if let pantryItem = pantryItems.first(where: { $0.id == ingredient.id }) {
                recentlyAddedItems.insert(pantryItem.id)
            }
        }

        // Clear recently added status after 5 seconds using structured concurrency
        Task {
            try await Task.sleep(nanoseconds: 5_000_000_000) // 5 seconds
            if !Task.isCancelled {
                recentlyAddedItems.removeAll()
                
                // Clear recent flags in storage
                try await storageService.clearRecentIngredients()
            }
        }
    }

    func addIngredient(_ ingredient: Ingredient) async {
        // No normalization - store ingredient name as-is, allow duplicates
        var newItem = ingredient
        newItem.dateAdded = Date()
        pantryItems.append(newItem)
        
        // Save to SwiftData storage
        do {
            try await storageService.saveIngredient(newItem)
        } catch {
            print("❌ Failed to save ingredient to storage: \(error)")
        }
    }

    func deletePantryItem(at offsets: IndexSet) async {
        let itemsToDelete = offsets.map { pantryItems[$0] }
        pantryItems.remove(atOffsets: offsets)
        
        // Delete from SwiftData storage
        for ingredient in itemsToDelete {
            await deleteIngredientFromStorage(ingredient)
        }
    }

    func deleteIngredient(_ ingredient: Ingredient) async {
        pantryItems.removeAll { $0.id == ingredient.id }
        await deleteIngredientFromStorage(ingredient)
    }

    func isRecentlyAdded(_ ingredient: Ingredient) -> Bool {
        return recentlyAddedItems.contains(ingredient.id)
    }

    func updateIngredient(_ ingredient: Ingredient, newName: String, newCategory: PantryCategory) async {
        if let index = pantryItems.firstIndex(where: { $0.id == ingredient.id }) {
            pantryItems[index].name = newName
            pantryItems[index].category = newCategory
            
            // Update in SwiftData storage
            do {
                // Delete old and save updated
                await deleteIngredientFromStorage(ingredient)
                try await storageService.saveIngredient(pantryItems[index])
            } catch {
                print("❌ Failed to update ingredient in storage: \(error)")
            }
        }
    }
    
    /// Update ingredient by ID - used by PantryOrganizerService
    func updateIngredient(id: UUID, newName: String, newCategory: PantryCategory) async {
        if let index = pantryItems.firstIndex(where: { $0.id == id }) {
            pantryItems[index].name = newName
            pantryItems[index].category = newCategory
            
            // Update in SwiftData storage
            do {
                // Delete old and save updated
                let oldIngredient = pantryItems[index]
                await deleteIngredientFromStorage(oldIngredient)
                try await storageService.saveIngredient(pantryItems[index])
            } catch {
                print("❌ Failed to update ingredient in storage: \(error)")
            }
        }
    }
    
    /// Delete ingredient by ID - used by PantryOrganizerService
    func deleteIngredient(id: UUID) async {
        if let ingredient = pantryItems.first(where: { $0.id == id }) {
            await deleteIngredient(ingredient)
        }
    }

    func deleteIngredients(_ ingredients: [Ingredient]) async {
        for ingredient in ingredients {
            await deleteIngredient(ingredient)
        }
    }

    /// Delete ingredients by explicit ID set to avoid accidental mass deletion
    func deleteIngredients(withIDs ids: Set<UUID>) async {
        // 🛡️ Safety Check 1: Validate input
        guard !ids.isEmpty else {
            print("⚠️ PantryService: No IDs provided for deletion")
            return
        }
        
        // 🛡️ Safety Check 2: Prevent accidental mass deletion
        let totalItems = pantryItems.count
        let requestedDeletions = ids.count
        // 🛡️ More reasonable threshold: only prevent if deleting 80% or more, or more than 20 items
        let maxDeletionThreshold = min(max(1, Int(Double(totalItems) * 0.8)), 20)
        
        print("🔍 PantryService DEBUG: Total items: \(totalItems), Requested deletions: \(requestedDeletions)")
        
        if requestedDeletions > maxDeletionThreshold {
            print("🚨 PantryService SAFETY: Preventing mass deletion of \(requestedDeletions) items (threshold: \(maxDeletionThreshold))")
            return
        }
        
        // 🔄 Perform deletion
        print("🗑️ PantryService: Deleting \(requestedDeletions) ingredients")
        pantryItems.removeAll { ids.contains($0.id) }
        
        // 💾 Save to persistent storage
        do {
            try await storageService.saveIngredients(pantryItems)
            print("✅ PantryService: Deletion completed and saved")
        } catch {
            print("❌ PantryService: Failed to save after deletion: \(error)")
        }
    }
    
    // 🔥 SIMPLE DELETE METHOD: No safety checks, just delete what user wants
    func deleteIngredientsSimple(withIDs ids: Set<UUID>) async {
        guard !ids.isEmpty else { return }
        
        print("🔥 PantryService SIMPLE: Deleting \(ids.count) ingredients - no safety checks")
        
        // Just delete the items
        pantryItems.removeAll { ids.contains($0.id) }
        
        // Save to storage using the correct method
        do {
            try await storageService.saveIngredients(pantryItems)
            print("✅ PantryService SIMPLE: Deletion completed")
        } catch {
            print("❌ PantryService SIMPLE: Failed to save after deletion: \(error)")
        }
    }
    
    // MARK: - Private Storage Methods
    
    private func loadPantryItems() async {
        do {
            let savedIngredients = try storageService.fetchAllIngredients()
            
            // Convert SavedIngredient to Ingredient, preserving persistent IDs
            pantryItems = savedIngredients.map { saved in
                let migratedCategory = PantryCategoryMigration.mapOldCategoryToNew(saved.category, ingredientName: saved.name)
                return Ingredient(
                    id: saved.id,
                    name: saved.name, // Use saved name as-is, no normalization
                    category: migratedCategory,
                    dateAdded: saved.dateAdded
                )
            }
            
            // Mark recent items
            let recentItems = savedIngredients.filter { $0.isRecent }
            recentlyAddedItems = Set(recentItems.map { $0.id })
            
            print("✅ Loaded \(pantryItems.count) pantry items from storage")
        } catch {
            print("❌ Failed to load pantry items: \(error)")
        }
    }
    
    private func deleteIngredientFromStorage(_ ingredient: Ingredient) async {
        do {
            let savedIngredients = try storageService.fetchAllIngredients()
            if let savedIngredient = savedIngredients.first(where: { $0.id == ingredient.id }) {
                try await storageService.deleteIngredient(savedIngredient)
            }
        } catch {
            print("❌ Failed to delete ingredient from storage: \(error)")
        }
    }
}

// MARK: - Category Migration

enum PantryCategoryMigration {
    static func mapOldCategoryToNew(_ raw: String, ingredientName: String) -> PantryCategory {
        let trimmed = raw.trimmingCharacters(in: .whitespacesAndNewlines)
        switch trimmed {
        case PantryCategory.bakingAndSweeteners.rawValue:
            return .bakingAndSweeteners
        case PantryCategory.oilsVinegarsAndCondiments.rawValue:
            return .oilsVinegarsAndCondiments
        case PantryCategory.spicesAndSeasonings.rawValue:
            return .spicesAndSeasonings
        case "Dry Goods":
            if IngredientNameNormalizer.isNutOrSeed(ingredientName) { return .nutsAndSeeds }
            return .grainsPastaLegumes
        case "Packaged Foods":
            return .cannedAndBroths
        case "Dairy & Alternatives":
            if IngredientNameNormalizer.isPlantBasedAlternative(ingredientName) { return .plantBasedAlternatives }
            return .dairy
        case "Pastry":
            return .bakery
        case "Snacks & Beverages":
            return .snacks
        case PantryCategory.proteins.rawValue:
            return .proteins
        case PantryCategory.produce.rawValue:
            return .produce
        default:
            return PantryCategory(rawValue: trimmed) ?? .other
        }
    }
}

// MARK: - Name Normalization

struct IngredientNameNormalizer {
    func normalize(_ name: String) -> String {
        let lower = name.lowercased().replacingOccurrences(of: " ", with: "")
        // Examples: unify diced tomatoes variants
        if lower.contains("dicedtomato") || lower.contains("切丁番茄") {
            return "canned tomatoes"
        }
        return canonicalLookup[name.lowercased()] ?? name.trimmingCharacters(in: .whitespacesAndNewlines)
    }
    static func isNutOrSeed(_ name: String) -> Bool {
        let n = name.lowercased()
        return ["almond","walnut","pecan","pistachio","cashew","peanut","hazelnut","chia","flax","sesame","sunflower","pumpkin"].contains { n.contains($0) }
    }
    static func isPlantBasedAlternative(_ name: String) -> Bool {
        let n = name.lowercased()
        return ["oat milk","almond milk","soy milk","coconut milk","rice milk","vegan","plant-based","tofu","tempeh","seitan","tvp"].contains { n.contains($0) }
    }
}

private let canonicalLookup: [String: String] = {
    // Minimal seed; can be expanded or loaded from file later
    [
        "dicedtomatoes": "canned tomatoes",
        "diced tomatoes": "canned tomatoes",
        "一罐切丁番茄": "canned tomatoes"
    ]
}()