import Foundation
import UIKit
import os.log

@MainActor
class ConcurrencyManager {
    static let shared = ConcurrencyManager()
    private let logger = Logger(subsystem: Bundle.main.bundleIdentifier ?? "IngredientScanner", category: "Concurrency")
    
    private init() {}
    
    // MARK: - Batch Image Processing
    
    func processBatchImages(
        _ images: [UIImage],
        using visionService: GoogleVisionAPIService,
        progressCallback: @escaping @MainActor (Int, Int) -> Void = { _, _ in }
    ) async throws -> [String] {
        logger.info("Starting batch processing of \(images.count) images")
        
        return try await withThrowingTaskGroup(of: IndexedResult<String>.self) { group in
            // Add tasks for each image
            for (index, image) in images.enumerated() {
                group.addTask { [index] in
                    do {
                        let result = try await visionService.detectTextAndLabels(in: image)
                        return IndexedResult(index: index, value: result.combinedContent)
                    } catch {
                        throw error
                    }
                }
            }
            
            // Collect results maintaining order
            var results: [IndexedResult<String>] = []
            var processedCount = 0
            
            for try await result in group {
                results.append(result)
                processedCount += 1
                
                // Update progress on main actor (no await needed since we're already on MainActor)
                progressCallback(processedCount, images.count)
            }
            
            // Sort by index and return values
            return results
                .sorted { $0.index < $1.index }
                .map { $0.value }
        }
    }
    
    // MARK: - Simple Async Operations
    
    func processImagePair(
        image1: UIImage,
        image2: UIImage,
        using visionService: GoogleVisionAPIService,
        progressCallback: @escaping @MainActor (Int, Int) -> Void
    ) async throws -> [String] {
        logger.info("Processing image pair")
        
        // Update progress (no await needed since we're already on MainActor)
        progressCallback(1, 2)
        
        let result1 = try await visionService.detectTextAndLabels(in: image1)
        let result2 = try await visionService.detectTextAndLabels(in: image2)
        
        progressCallback(2, 2)
        
        return [result1.combinedContent, result2.combinedContent]
    }
}

// MARK: - Supporting Types

private struct IndexedResult<T: Sendable>: Sendable {
    let index: Int
    let value: T
} 