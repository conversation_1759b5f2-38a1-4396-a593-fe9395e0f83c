import Foundation
import SwiftUI

@Observable
@MainActor
class ServiceContainer {
    static let shared = ServiceContainer()

    // Core services
    let pantryService = PantryService()
    let authenticationService = AuthenticationService()
    let swiftDataStorage = SwiftDataStorageService.shared
    let concurrencyManager = ConcurrencyManager.shared
    let enhancedCameraService = EnhancedCameraService()

    // API services (actors)
    let googleVisionService = GoogleVisionAPIService()
    let geminiService = GeminiAPIService()
    let recipeGenerationService = RecipeGenerationService()
    
    // Organizer service (initialized lazily due to dependencies)
    private var _pantryOrganizerService: PantryOrganizerService?
    
    var pantryOrganizerService: PantryOrganizerService {
        if _pantryOrganizerService == nil {
            _pantryOrganizerService = PantryOrganizerService(
                geminiService: geminiService,
                pantryService: pantryService
            )
        }
        return _pantryOrganizerService!
    }

    private init() {
        // Initialize any necessary configurations
    }
}
