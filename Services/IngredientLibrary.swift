import Foundation

// A lightweight in-memory ingredient library loaded from ingredients.txt at startup.
// Provides prefix search and category grouping.
@MainActor
final class IngredientLibrary {
    static let shared = IngredientLibrary()
    private(set) var categoryToItems: [PantryCategory: [String]] = [:]
    private(set) var nameToCategory: [String: PantryCategory] = [:]
    private(set) var allIngredientNames: [String] = []

    private init() {
        loadFromBundledFile()
    }

    func items(for category: PantryCategory) -> [String] {
        categoryToItems[category] ?? []
    }

    func suggest(prefix: String, limit: Int = 10) -> [String] {
        if prefix.isEmpty { return [] }
        let lower = prefix.lowercased()
        return allIngredientNames.filter { $0.lowercased().hasPrefix(lower) }.prefix(limit).map { String($0) }
    }

    func group(names: [String]) -> [(String, PantryCategory)] {
        names.compactMap { name in
            guard let category = nameToCategory[name] ?? nameToCategory[name.lowercased()] else { return nil }
            return (name, category)
        }
    }

    private func loadFromBundledFile() {
        // Try to load the full taxonomy from ingredients.txt at project root
        // This file should be included in the app bundle for production
        if let url = Bundle.main.url(forResource: "ingredients", withExtension: "txt"),
           let content = try? String(contentsOf: url) {
            parseIngredientsText(content)
            return
        }

        // Dev fallback: load from workspace path if running in debug without bundle resource
        let workspacePath = "\(NSHomeDirectory())/Desktop/ingredient-scanner/ingredients.txt"
        if let content = try? String(contentsOfFile: workspacePath, encoding: .utf8) {
            parseIngredientsText(content)
            return
        }

        // Last resort: leave empty structures
        categoryToItems = [:]
        nameToCategory = [:]
        allIngredientNames = []
    }

    private func parseIngredientsText(_ text: String) {
        var currentCategory: PantryCategory = .other
        var map: [PantryCategory: [String]] = [:]
        for line in text.components(separatedBy: .newlines) {
            let trimmed = line.trimmingCharacters(in: .whitespaces)
            if trimmed.isEmpty { continue }
            if trimmed.hasPrefix("Category:") {
                let label = trimmed.replacingOccurrences(of: "Category:", with: "").trimmingCharacters(in: .whitespaces)
                currentCategory = PantryCategory(rawValue: label)
                    ?? PantryCategoryMigration.mapOldCategoryToNew(label, ingredientName: "")
                continue
            }
            if trimmed.hasPrefix("-") {
                let name = trimmed.dropFirst().trimmingCharacters(in: .whitespaces)
                guard !name.isEmpty else { continue }
                map[currentCategory, default: []].append(name)
                nameToCategory[name] = currentCategory
                nameToCategory[name.lowercased()] = currentCategory
            }
        }
        for (cat, items) in map {
            categoryToItems[cat] = Array(Set(items)).sorted { $0.localizedCaseInsensitiveCompare($1) == .orderedAscending }
        }
        allIngredientNames = categoryToItems.values.flatMap { $0 }.sorted { $0.localizedCaseInsensitiveCompare($1) == .orderedAscending }
    }
}
