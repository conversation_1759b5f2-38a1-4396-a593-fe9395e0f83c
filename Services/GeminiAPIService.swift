import Foundation
import UIKit

actor <PERSON><PERSON>IService {
    private let apiKey = APIKeys.geminiAPIKey
    private let baseURL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent"
    
    // MARK: - Type Aliases for Task 31
    typealias VisionOutput = GoogleVisionAPIService.VisionOutput
    
    // MARK: - Ingredient Canonicalization (New for Task 31)
    
    /// Process Vision outputs and return standardized ingredients with strict category enforcement
    func canonicalizeIngredients(
        visionOutputs: [VisionOutput],
        allowedCategories: [String]
    ) async throws -> [Ingredient] {
        // Validate inputs
        guard !visionOutputs.isEmpty else {
            throw GeminiError.invalidInput("Vision outputs cannot be empty")
        }
        
        guard !allowedCategories.isEmpty else {
            throw GeminiError.invalidInput("Allowed categories cannot be empty")
        }
        
        // Construct prompt with vision outputs and allowed categories
        let prompt = buildCanonicalizePrompt(visionOutputs: visionOutputs, allowedCategories: allowedCategories)
        
        // Call Gemini API
        let response = try await callGeminiAPI(prompt: prompt)
        
        // Parse response into ingredients
        return try parseIngredientsResponse(response, allowedCategories: allowedCategories)
    }
    
    /// Build a structured prompt for ingredient canonicalization
    private func buildCanonicalizePrompt(visionOutputs: [VisionOutput], allowedCategories: [String]) -> String {
        var prompt = """
        You are a food ingredient analyzer. Process the following OCR text and labels from images of food items, receipts, or packaging.
        
        For each detected item:
        1. Extract the food ingredient name, removing brand names, sizes, quantities, and marketing text
        2. Preserve meaningful descriptors (e.g., "whole milk" instead of just "milk")
        3. Categorize each item into EXACTLY ONE of these categories: \(allowedCategories.joined(separator: ", "))
        4. Exclude any non-food items or gibberish text entirely
        
        Return ONLY a JSON array with this format: [{"name":"ingredient name","category":"exact category"}]
        Do not include any explanation or additional text.
        """
        
        // Add vision outputs to prompt
        for (index, output) in visionOutputs.enumerated() {
            prompt += "\n\nIMAGE \(index + 1):\nOCR TEXT: \(output.ocrText)\nLABELS: \(output.labels.joined(separator: ", "))"
        }
        
        return prompt
    }
    
    /// Parse Gemini API response into Ingredient objects with strict validation
    private func parseIngredientsResponse(_ response: String, allowedCategories: [String]) throws -> [Ingredient] {
        // Extract JSON array from response
        guard let jsonData = response.data(using: .utf8) else {
            throw GeminiError.invalidResponseWithMessage("Could not convert response to data")
        }
        
        // Decode JSON
        struct GeminiIngredient: Decodable {
            let name: String
            let category: String
        }
        
        let decoder = JSONDecoder()
        let geminiIngredients: [GeminiIngredient]
        
        do {
            geminiIngredients = try decoder.decode([GeminiIngredient].self, from: jsonData)
        } catch {
            throw GeminiError.parsingErrorWithMessage("Failed to decode JSON response: \(error.localizedDescription)")
        }
        
        // Convert to Ingredient objects with validation and quality checks
        let validIngredients = geminiIngredients.compactMap { geminiIngredient -> Ingredient? in
            // Quality checks for ingredient name
            let cleanedName = geminiIngredient.name.trimmingCharacters(in: .whitespacesAndNewlines)
            
            // Filter out invalid ingredient names
            guard isValidIngredientName(cleanedName) else {
                return nil
            }
            
            // Verify category is allowed
            guard allowedCategories.contains(geminiIngredient.category) else {
                return nil
            }
            
            // Create Ingredient with valid category
            guard let category = PantryCategory(rawValue: geminiIngredient.category) else {
                return nil
            }
            
            return Ingredient(
                id: UUID(),
                name: cleanedName,
                category: category
            )
        }
        
        return validIngredients
    }
    
    /// Validate ingredient name quality
    private func isValidIngredientName(_ name: String) -> Bool {
        // Must have reasonable length
        guard name.count >= 2 && name.count <= 100 else {
            return false
        }
        
        // Must not be purely numeric
        guard !name.allSatisfy({ $0.isNumber || $0.isWhitespace }) else {
            return false
        }
        
        // Must not have excessive punctuation
        let punctuationCount = name.filter { $0.isPunctuation }.count
        guard Double(punctuationCount) / Double(name.count) <= 0.5 else {
            return false
        }
        
        // Must contain at least one letter
        guard name.contains(where: { $0.isLetter }) else {
            return false
        }
        
        return true
    }
    
    /// Make API call to Gemini service
    func callGeminiAPI(prompt: String) async throws -> String {
        guard apiKey != "YOUR_GEMINI_API_KEY_HERE" && !apiKey.isEmpty else {
            throw GeminiError.apiKeyNotConfigured("Gemini API key not configured. Please update APIKeys.swift")
        }
        
        let requestBody: [String: Any] = [
            "contents": [
                [
                    "parts": [
                        ["text": prompt]
                    ]
                ]
            ]
        ]
        
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: requestBody)
            
            guard let url = URL(string: "\(baseURL)?key=\(apiKey)") else {
                throw GeminiError.invalidURL
            }
            
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            request.httpBody = jsonData
            request.timeoutInterval = 30.0 // Add timeout
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                throw GeminiError.invalidResponseWithMessage("Invalid HTTP response")
            }
            
            guard httpResponse.statusCode == 200 else {
                if httpResponse.statusCode == 403 {
                    throw GeminiError.apiKeyNotConfigured("Gemini API key is invalid or access denied (403)")
                } else if httpResponse.statusCode == 429 {
                    throw GeminiError.rateLimitExceeded("Rate limit exceeded. Please try again later.")
                } else {
                    throw GeminiError.apiError("HTTP \(httpResponse.statusCode)")
                }
            }
            
            guard let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                  let candidates = json["candidates"] as? [[String: Any]],
                  let firstCandidate = candidates.first,
                  let content = firstCandidate["content"] as? [String: Any],
                  let parts = content["parts"] as? [[String: Any]],
                  let firstPart = parts.first,
                  let text = firstPart["text"] as? String else {
                throw GeminiError.parsingErrorWithMessage("Invalid response structure from Gemini API")
            }
            
            // Clean the text and remove code block markers
            let cleanedText = text.trimmingCharacters(in: .whitespacesAndNewlines)
                .replacingOccurrences(of: "```json", with: "")
                .replacingOccurrences(of: "```", with: "")
                .trimmingCharacters(in: .whitespacesAndNewlines)
            
            return cleanedText
            
        } catch {
            if error is GeminiError {
                throw error
            }
            throw GeminiError.networkErrorWithMessage("Network request failed: \(error.localizedDescription)")
        }
    }
    
    // MARK: - Legacy Methods (Existing functionality)
    
    func extractIngredients(from visionText: String) async throws -> [Ingredient] {
        guard apiKey != "YOUR_GEMINI_API_KEY_HERE" && !apiKey.isEmpty else {
            throw GeminiError.apiKeyNotConfigured("Gemini API key not configured. Please update APIKeys.swift")
        }
        
        let prompt = """
        You are an expert food analyst. Extract individual ingredients from this text and return them as a JSON array of objects with "name" and "category" fields.

        Categories should be one of: vegetables, fruits, proteins, grains, dairy, spices, oils, condiments, plant-based, canned, broths, bakery, snacks, other. If unsure, use other.

        Text to analyze:
        \(visionText)

        Return only valid JSON array, no other text:
        """
        
        let requestBody: [String: Any] = [
            "contents": [
                [
                    "parts": [
                        ["text": prompt]
                    ]
                ]
            ]
        ]
        
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: requestBody)
            
            guard let url = URL(string: "\(baseURL)?key=\(apiKey)") else {
                throw GeminiError.invalidURL
            }
            
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            request.httpBody = jsonData
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                throw GeminiError.invalidResponse
            }
            
            guard httpResponse.statusCode == 200 else {
                if httpResponse.statusCode == 403 {
                    throw GeminiError.apiKeyNotConfigured("Gemini API key is invalid or access denied (403)")
                } else {
                    throw GeminiError.invalidResponse
                }
            }
            
            guard let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                  let candidates = json["candidates"] as? [[String: Any]],
                  let firstCandidate = candidates.first,
                  let content = firstCandidate["content"] as? [String: Any],
                  let parts = content["parts"] as? [[String: Any]],
                  let firstPart = parts.first,
                  let text = firstPart["text"] as? String else {
                throw GeminiError.parsingError
            }
            
            // Clean the text and parse JSON
            let cleanedText = text.trimmingCharacters(in: .whitespacesAndNewlines)
                .replacingOccurrences(of: "```json", with: "")
                .replacingOccurrences(of: "```", with: "")
                .trimmingCharacters(in: .whitespacesAndNewlines)
            
            guard let ingredientsData = cleanedText.data(using: .utf8),
                  let ingredientObjects = try JSONSerialization.jsonObject(with: ingredientsData) as? [[String: Any]] else {
                throw GeminiError.parsingError
            }
            
            var ingredients: [Ingredient] = []
            for ingredientObj in ingredientObjects {
                guard let name = ingredientObj["name"] as? String,
                      let categoryString = ingredientObj["category"] as? String else {
                    continue
                }
                
                let normalizedCategory = GeminiCategoryMapper.map(categoryString: categoryString, name: name)
                ingredients.append(Ingredient(name: name, category: normalizedCategory))
            }
            
            return ingredients
            
        } catch {
            if error is GeminiError {
                throw error
            }
            throw GeminiError.parsingError
        }
    }
    
    func analyzeIngredients(in image: UIImage) async throws -> String {
        guard apiKey != "YOUR_GEMINI_API_KEY_HERE" && !apiKey.isEmpty else {
            throw GeminiError.apiKeyNotConfigured("Gemini API key not configured. Please update APIKeys.swift")
        }
        
        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
            throw GeminiError.invalidImage
        }
        
        let base64Image = imageData.base64EncodedString()
        
        let prompt = "Analyze this image and identify all visible food ingredients. List them clearly."
        
        let requestBody: [String: Any] = [
            "contents": [
                [
                    "parts": [
                        ["text": prompt],
                        [
                            "inline_data": [
                                "mime_type": "image/jpeg",
                                "data": base64Image
                            ]
                        ]
                    ]
                ]
            ]
        ]
        
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: requestBody)
            
            guard let url = URL(string: "\(baseURL)?key=\(apiKey)") else {
                throw GeminiError.invalidURL
            }
            
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            request.httpBody = jsonData
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                throw GeminiError.invalidResponse
            }
            
            guard httpResponse.statusCode == 200 else {
                if httpResponse.statusCode == 403 {
                    throw GeminiError.apiKeyNotConfigured("Gemini API key is invalid or access denied (403)")
                } else {
                    throw GeminiError.invalidResponse
                }
            }
            
            guard let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                  let candidates = json["candidates"] as? [[String: Any]],
                  let firstCandidate = candidates.first,
                  let content = firstCandidate["content"] as? [String: Any],
                  let parts = content["parts"] as? [[String: Any]],
                  let firstPart = parts.first,
                  let text = firstPart["text"] as? String else {
                throw GeminiError.parsingError
            }
            
            return text
            
        } catch {
            if error is GeminiError {
                throw error
            }
            throw GeminiError.networkError
        }
    }
}

// MARK: - Error Types (Enhanced for Task 31)

enum GeminiError: Error, LocalizedError {
    case apiKeyNotConfigured(String)
    case invalidURL
    case invalidImage
    case invalidResponseWithMessage(String)
    case parsingErrorWithMessage(String)
    case networkErrorWithMessage(String)
    case invalidInput(String)
    case apiError(String)
    case rateLimitExceeded(String)
    
    // Legacy cases for backward compatibility
    case invalidResponse
    case parsingError
    case networkError
    
    var errorDescription: String? {
        switch self {
        case .apiKeyNotConfigured(let message):
            return message
        case .invalidURL:
            return "Invalid Gemini API URL"
        case .invalidImage:
            return "Invalid image data"
        case .invalidResponseWithMessage(let message):
            return "Invalid response from Gemini API: \(message)"
        case .parsingErrorWithMessage(let message):
            return "Failed to parse Gemini response: \(message)"
        case .networkErrorWithMessage(let message):
            return "Network error occurred: \(message)"
        case .invalidInput(let message):
            return "Invalid input: \(message)"
        case .apiError(let message):
            return "API error: \(message)"
        case .rateLimitExceeded(let message):
            return "Rate limit exceeded: \(message)"
        // Legacy cases
        case .invalidResponse:
            return "Invalid response from Gemini API"
        case .parsingError:
            return "Failed to parse Gemini response"
        case .networkError:
            return "Network error occurred"
        }
    }
}

// MARK: - Gemini Category Mapper (Legacy)

enum GeminiCategoryMapper {
    static func map(categoryString: String, name: String) -> PantryCategory {
        let c = categoryString.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)
        switch c {
        case "vegetables", "fruits", "produce":
            return .produce
        case "proteins", "protein":
            return .proteins
        case "grains", "pasta", "legumes":
            return .grainsPastaLegumes
        case "dairy":
            if IngredientNameNormalizer.isPlantBasedAlternative(name) { return .plantBasedAlternatives }
            return .dairy
        case "spices", "seasonings":
            return .spicesAndSeasonings
        case "oils", "condiments":
            return .oilsVinegarsAndCondiments
        case "plant-based", "vegan":
            return .plantBasedAlternatives
        case "bakery", "pastry":
            return .bakery
        case "canned", "broths", "packaged":
            return .cannedAndBroths
        case "snacks":
            return .snacks
        default:
            return .other
        }
    }
}