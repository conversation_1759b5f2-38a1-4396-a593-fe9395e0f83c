import SwiftUI
import VisionKit
import UIKit
import AVFoundation

/// iOS 17 Enhanced Camera View with advanced features
struct EnhancedCameraView: View {
    @State private var cameraService = EnhancedCameraService()
    @State private var showingDocumentScanner = false
    @State private var showingImagePicker = false
    @State private var showingUltraWideOption = false
    @State private var selectedSourceType: UIImagePickerController.SourceType = .camera
    
    let onImagesSelected: ([UIImage]) -> Void
    let maxImages: Int
    
    var body: some View {
        VStack(spacing: 20) {
            // Header
            VStack(spacing: 8) {
                Image(systemName: "camera.fill")
                    .font(.system(size: 50))
                    .foregroundColor(.blue)
                
                Text("Enhanced Camera")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("Powered by iOS 17 Vision")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            // Camera Options
            VStack(spacing: 16) {
                // Standard Camera Button
                Button(action: {
                    selectedSourceType = .camera
                    showingImagePicker = true
                }) {
                    HStack {
                        Image(systemName: "camera")
                        Text("Standard Camera")
                        Spacer()
                        Image(systemName: "chevron.right")
                    }
                    .padding()
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(12)
                }
                
                // Document Scanner Button
                Button(action: {
                    showingDocumentScanner = true
                }) {
                    HStack {
                        Image(systemName: "doc.text.viewfinder")
                        VStack(alignment: .leading, spacing: 2) {
                            Text("Document Scanner")
                            Text("Perfect for ingredient lists")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        Spacer()
                        Image(systemName: "chevron.right")
                    }
                    .padding()
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(12)
                }
                
                // Ultra Wide Camera Button (if available)
                if hasUltraWideCamera {
                    Button(action: {
                        showingUltraWideOption = true
                    }) {
                        HStack {
                            Image(systemName: "camera.macro")
                            VStack(alignment: .leading, spacing: 2) {
                                Text("Ultra Wide Camera")
                                Text("For small text and detailed ingredients")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            Spacer()
                            Image(systemName: "chevron.right")
                        }
                        .padding()
                        .background(Color.purple.opacity(0.1))
                        .cornerRadius(12)
                    }
                }
                
                // Photo Library Button
                Button(action: {
                    selectedSourceType = .photoLibrary
                    showingImagePicker = true
                }) {
                    HStack {
                        Image(systemName: "photo.on.rectangle")
                        Text("Choose from Library")
                        Spacer()
                        Image(systemName: "chevron.right")
                    }
                    .padding()
                    .background(Color.orange.opacity(0.1))
                    .cornerRadius(12)
                }
            }
            
            // Tips Section
            VStack(alignment: .leading, spacing: 8) {
                Text("💡 Tips for Better Results")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                TipView(icon: "lightbulb.fill", text: "Ensure good lighting for text recognition")
                TipView(icon: "viewfinder", text: "Keep ingredient labels clearly visible")
                TipView(icon: "hand.raised.fill", text: "Hold steady while capturing")
                TipView(icon: "magnifyingglass", text: "Use Ultra Wide for small text")
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
            
            Spacer()
        }
        .padding()
        .sheet(isPresented: $showingImagePicker) {
            ImagePicker(selectedImage: .constant(nil), sourceType: selectedSourceType)
                .onReceive(NotificationCenter.default.publisher(for: .imagePickerDidSelectImage)) { notification in
                    if let image = notification.object as? UIImage {
                        Task {
                            await processSelectedImage(image)
                        }
                    }
                }
        }
        .sheet(isPresented: $showingDocumentScanner) {
            DocumentCameraView { result in
                switch result {
                case .success(let images):
                    Task {
                        await processSelectedImages(images)
                    }
                case .failure(let error):
                    print("Document scanning failed: \(error.localizedDescription)")
                }
            }
        }
        .alert("Ultra Wide Camera", isPresented: $showingUltraWideOption) {
            Button("Capture") {
                Task {
                    await captureWithUltraWide()
                }
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("Use Ultra Wide camera for capturing small text and detailed ingredient information.")
        }
    }
    
    // MARK: - Computed Properties
    private var hasUltraWideCamera: Bool {
        let discoverySession = AVCaptureDevice.DiscoverySession(
            deviceTypes: [.builtInUltraWideCamera],
            mediaType: .video,
            position: .back
        )
        return !discoverySession.devices.isEmpty
    }
    
    // MARK: - Image Processing Methods
    @MainActor
    private func processSelectedImage(_ image: UIImage) async {
        do {
            let result = try await cameraService.processImageWithEnhancedVision(image)
            print("✅ Enhanced Vision Result: \(result.qualityMetrics)")
            onImagesSelected([image])
        } catch {
            print("❌ Vision processing failed: \(error.localizedDescription)")
            // Still pass the image for fallback processing
            onImagesSelected([image])
        }
    }
    
    @MainActor
    private func processSelectedImages(_ images: [UIImage]) async {
        var processedImages: [UIImage] = []
        
        for image in images {
            do {
                let result = try await cameraService.processImageWithEnhancedVision(image)
                print("✅ Enhanced Vision Result: \(result.qualityMetrics)")
                processedImages.append(image)
            } catch {
                print("❌ Vision processing failed: \(error.localizedDescription)")
                // Still include the image for fallback processing
                processedImages.append(image)
            }
        }
        
        onImagesSelected(processedImages)
    }
    
    @MainActor
    private func captureWithUltraWide() async {
        do {
            if let image = try await cameraService.captureWithUltraWideCamera() {
                await processSelectedImage(image)
            }
        } catch {
            print("❌ Ultra Wide capture failed: \(error.localizedDescription)")
        }
    }
}

// MARK: - Supporting Views
struct TipView: View {
    let icon: String
    let text: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .frame(width: 20)
            
            Text(text)
                .font(.body)
                .foregroundColor(.primary)
            
            Spacer()
        }
    }
}

// MARK: - Document Camera Wrapper
struct DocumentCameraView: UIViewControllerRepresentable {
    let completion: (Result<[UIImage], Error>) -> Void
    
    func makeUIViewController(context: Context) -> VNDocumentCameraViewController {
        let scanner = VNDocumentCameraViewController()
        scanner.delegate = context.coordinator
        return scanner
    }
    
    func updateUIViewController(_ uiViewController: VNDocumentCameraViewController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(completion: completion)
    }
    
    class Coordinator: NSObject, VNDocumentCameraViewControllerDelegate {
        let completion: (Result<[UIImage], Error>) -> Void
        
        init(completion: @escaping (Result<[UIImage], Error>) -> Void) {
            self.completion = completion
        }
        
        nonisolated func documentCameraViewController(_ controller: VNDocumentCameraViewController, didFinishWith scan: VNDocumentCameraScan) {
            var images: [UIImage] = []
            
            for pageIndex in 0..<scan.pageCount {
                let image = scan.imageOfPage(at: pageIndex)
                images.append(image)
            }
            
            completion(.success(images))
            Task { @MainActor in
                controller.dismiss(animated: true)
            }
        }
        
        nonisolated func documentCameraViewController(_ controller: VNDocumentCameraViewController, didFailWithError error: Error) {
            completion(.failure(error))
            Task { @MainActor in
                controller.dismiss(animated: true)
            }
        }
        
        nonisolated func documentCameraViewControllerDidCancel(_ controller: VNDocumentCameraViewController) {
            completion(.failure(CameraError.captureFailed))
            Task { @MainActor in
                controller.dismiss(animated: true)
            }
        }
    }
}



// MARK: - Preview
#Preview {
    EnhancedCameraView(
        onImagesSelected: { images in
            print("Selected \(images.count) images")
        },
        maxImages: 3
    )
} 