import SwiftUI
import AVFoundation
import Vision
import VisionKit
import UIKit

/// Enhanced Camera Service leveraging iOS 17 capabilities
@MainActor
@Observable
class EnhancedCameraService: NSObject {
    
    // MARK: - Published Properties
    var capturedImages: [UIImage] = []
    var isCapturing = false
    var hasPermission = false
    var showingDocumentScanner = false
    var showingCamera = false
    
    // MARK: - Private Properties
    private let visionService = GoogleVisionAPIService()
    private var documentScannerCompletion: ((Result<[UIImage], Error>) -> Void)?
    
    // MARK: - Camera Permission Handling
    func requestCameraPermission() async -> Bool {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized:
            hasPermission = true
            return true
        case .notDetermined:
            let granted = await AVCaptureDevice.requestAccess(for: .video)
            hasPermission = granted
            return granted
        case .denied, .restricted:
            hasPermission = false
            return false
        @unknown default:
            hasPermission = false
            return false
        }
    }
    
    // MARK: - Enhanced Camera Capture
    func captureWithEnhancedCamera() async throws -> UIImage? {
        guard await requestCameraPermission() else {
            throw CameraError.permissionDenied
        }
        
        // This will be integrated with the new iOS 17 Camera API
        // For now, we'll use a placeholder that can be enhanced with the actual implementation
        showingCamera = true
        return nil
    }
    
    // MARK: - Document Scanner Integration
    func scanDocumentForIngredients() {
        // VNDocumentCameraViewController is always available on iOS 13+
        // No need to check availability for VisionKit document scanner
        showingDocumentScanner = true
    }
    
    // MARK: - Ultra Wide Camera Support
    func captureWithUltraWideCamera() async throws -> UIImage? {
        guard await requestCameraPermission() else {
            throw CameraError.permissionDenied
        }
        
        // Check if Ultra Wide camera is available
        let discoverySession = AVCaptureDevice.DiscoverySession(
            deviceTypes: [.builtInUltraWideCamera],
            mediaType: .video,
            position: .back
        )
        
        guard !discoverySession.devices.isEmpty else {
            throw CameraError.ultraWideCameraNotAvailable
        }
        
        // Implementation for Ultra Wide camera capture
        // This would integrate with the new Camera API
        return nil
    }
    
    // MARK: - Enhanced Vision Processing
    func processImageWithEnhancedVision(_ image: UIImage) async throws -> EnhancedVisionResult {
        // Create Vision requests for iOS 17 enhanced features
        let textRequest = VNRecognizeTextRequest()
        textRequest.recognitionLevel = .accurate
        textRequest.usesLanguageCorrection = true
        
        // New iOS 17 features
        let contourRequest = VNDetectContoursRequest()
        contourRequest.contrastAdjustment = 1.0
        contourRequest.detectsDarkOnLight = true
        
        guard let cgImage = image.cgImage else {
            throw CameraError.invalidImage
        }
        
        let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
        
        // Perform enhanced vision analysis
        try handler.perform([textRequest, contourRequest])
        
        // Extract results
        let textObservations = textRequest.results ?? []
        let contourObservations = contourRequest.results ?? []
        
        let recognizedText = textObservations.compactMap { observation in
            observation.topCandidates(1).first?.string
        }.joined(separator: " ")
        
        // Also get Google Vision results for comparison
        let googleVisionResult = try await visionService.detectTextAndLabels(in: image)
        
        return EnhancedVisionResult(
            localVisionText: recognizedText,
            googleVisionResult: googleVisionResult,
            contourCount: contourObservations.count,
            confidence: calculateAverageConfidence(textObservations)
        )
    }
    
    // MARK: - Helper Methods
    private func calculateAverageConfidence(_ observations: [VNRecognizedTextObservation]) -> Float {
        guard !observations.isEmpty else { return 0.0 }
        
        let totalConfidence = observations.compactMap { observation in
            observation.topCandidates(1).first?.confidence
        }.reduce(0, +)
        
        return totalConfidence / Float(observations.count)
    }
}

// MARK: - Enhanced Vision Result
struct EnhancedVisionResult {
    let localVisionText: String
    let googleVisionResult: GoogleVisionAPIService.VisionResult
    let contourCount: Int
    let confidence: Float
    
    var combinedText: String {
        var combined = localVisionText
        if !googleVisionResult.detectedText.isEmpty {
            combined += "\n\n--- Google Vision ---\n" + googleVisionResult.detectedText
        }
        if !googleVisionResult.detectedLabels.isEmpty {
            combined += "\n\nDetected Items: " + googleVisionResult.detectedLabels.joined(separator: ", ")
        }
        return combined
    }
    
    var qualityMetrics: String {
        return "Confidence: \(String(format: "%.1f%%", confidence * 100)), Contours: \(contourCount)"
    }
}

// MARK: - Camera Errors
enum CameraError: LocalizedError {
    case permissionDenied
    case ultraWideCameraNotAvailable
    case invalidImage
    case captureFailed
    
    var errorDescription: String? {
        switch self {
        case .permissionDenied:
            return "Camera permission is required to capture images"
        case .ultraWideCameraNotAvailable:
            return "Ultra Wide camera is not available on this device"
        case .invalidImage:
            return "Unable to process the captured image"
        case .captureFailed:
            return "Failed to capture image"
        }
    }
}

// MARK: - VNDocumentCameraViewControllerDelegate
extension EnhancedCameraService: VNDocumentCameraViewControllerDelegate {
    nonisolated func documentCameraViewController(_ controller: VNDocumentCameraViewController, didFinishWith scan: VNDocumentCameraScan) {
        var scannedImages: [UIImage] = []
        
        for pageIndex in 0..<scan.pageCount {
            let image = scan.imageOfPage(at: pageIndex)
            scannedImages.append(image)
        }
        
        Task { @MainActor in
            capturedImages.append(contentsOf: scannedImages)
            documentScannerCompletion?(.success(scannedImages))
        }
        
        Task { @MainActor in
            controller.dismiss(animated: true)
        }
    }
    
    nonisolated func documentCameraViewController(_ controller: VNDocumentCameraViewController, didFailWithError error: Error) {
        Task { @MainActor in
            documentScannerCompletion?(.failure(error))
            controller.dismiss(animated: true)
        }
    }
    
    nonisolated func documentCameraViewControllerDidCancel(_ controller: VNDocumentCameraViewController) {
        Task { @MainActor in
            documentScannerCompletion?(.failure(CameraError.captureFailed))
            controller.dismiss(animated: true)
        }
    }
} 