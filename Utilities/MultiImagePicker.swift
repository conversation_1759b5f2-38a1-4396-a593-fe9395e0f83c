import SwiftUI
@preconcurrency import Photos<PERSON>

// Extension to add async support for NSItemProvider
extension NSItemProvider {
    func loadImage() async throws -> UIImage? {
        return try await withCheckedThrowingContinuation { continuation in
            self.loadObject(ofClass: UIImage.self) { object, error in
                if let error = error {
                    continuation.resume(throwing: error)
                } else if let image = object as? UIImage {
                    continuation.resume(returning: image)
                } else {
                    continuation.resume(returning: nil)
                }
            }
        }
    }
}

struct MultiImagePicker: UIViewControllerRepresentable {
    @Binding var selectedImages: [UIImage]
    let maxSelection: Int
    @Environment(\.presentationMode) private var presentationMode
    
    func makeUIViewController(context: Context) -> PHPickerViewController {
        var config = PHPickerConfiguration()
        config.selectionLimit = maxSelection
        config.filter = .images
        config.preferredAssetRepresentationMode = .current
        
        let picker = PHPickerViewController(configuration: config)
        picker.delegate = context.coordinator
        return picker
    }
    
    func updateUIViewController(_ uiViewController: PHPickerViewController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, PHPickerViewControllerDelegate {
        let parent: MultiImagePicker
        
        init(_ parent: MultiImagePicker) {
            self.parent = parent
            super.init()
        }
        
        func picker(_ picker: PHPickerViewController, didFinishPicking results: [PHPickerResult]) {
            // Dismiss immediately
            Task { @MainActor in
                parent.presentationMode.wrappedValue.dismiss()
            }
            
            guard !results.isEmpty else { return }
            
            // Process images
            let providers = results.map { $0.itemProvider }
            
            Task {
                var loadedImages: [UIImage] = []
                
                for provider in providers {
                    if provider.canLoadObject(ofClass: UIImage.self) {
                        do {
                            if let image = try await provider.loadImage() {
                                loadedImages.append(image)
                            }
                        } catch {
                            print("Error loading image: \(error.localizedDescription)")
                        }
                    }
                }
                
                // Update UI on main thread
                await MainActor.run {
                    self.parent.selectedImages = loadedImages
                }
            }
        }
    }
} 