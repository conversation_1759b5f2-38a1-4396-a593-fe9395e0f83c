import Foundation
@preconcurrency import FirebaseFirestore

/// User preferences for ingredient scanner app
/// 
/// Contains both app settings and recipe generation preferences:
/// - User settings (theme, notifications, user info)
/// - Dietary restrictions (allergies, dietary choices) 
/// - Family size (for portion planning)
/// - Preference to respect restrictions in recipe generation
///
/// Enhanced for Firestore compatibility with proper Codable support
struct UserPreferences: Codable, Equatable, Sendable {
    
    // MARK: - Firestore Integration
    
    /// Document ID for Firestore (automatically managed by Firebase)
    var documentID: String?
    
    // MARK: - User Settings (Task 9 Requirements)
    
    /// User ID for Firestore sync
    var userId: String
    
    /// App theme setting
    var theme: String
    
    /// Notification preferences
    var notifications: Bool
    
    /// Last updated timestamp
    var lastUpdated: Date
    
    /// Created timestamp for new users
    var createdAt: Date
    
    // MARK: - Family Information
    
    /// Number of people in household (for recipe servings)
    var familySize: Int
    
    /// Family member names and ages (for portion customization)
    var familyMembers: [FamilyMember]
    
    // MARK: - Recipe Preferences (Enhanced)
    
    /// Dietary restrictions and allergies
    var dietaryRestrictions: [DietaryRestriction]
    
    /// Strict exclusions (foods to never include)
    var strictExclusions: [StrictExclusion]
    
    /// Allergies and intolerances (medical restrictions)
    var allergiesIntolerances: [AllergyIntolerance]
    
    /// Whether to respect dietary restrictions in recipe generation
    var respectRestrictions: Bool
    
    // MARK: - Computed Properties
    
    /// Returns true if user has any dietary restrictions
    var hasRestrictions: Bool {
        return !dietaryRestrictions.isEmpty || !strictExclusions.isEmpty || !allergiesIntolerances.isEmpty
    }
    
    /// Returns formatted string of dietary restrictions for display
    var restrictionsDisplayText: String {
        let allRestrictions = dietaryRestrictions.map { $0.rawValue } + 
                            strictExclusions.map { $0.rawValue } + 
                            allergiesIntolerances.map { $0.rawValue }
        if allRestrictions.isEmpty {
            return "None"
        }
        return allRestrictions.joined(separator: ", ")
    }
    
    /// Returns count of all restrictions for UI display
    var restrictionsCount: Int {
        return dietaryRestrictions.count + strictExclusions.count + allergiesIntolerances.count
    }
    
    /// Returns count of active allergies
    var allergiesCount: Int {
        return allergiesIntolerances.count
    }
    
    // MARK: - Backward Compatibility Properties
    
    /// Backward compatibility: returns allergies as string array
    var allergies: [String] {
        return allergiesIntolerances.map { $0.rawValue }
    }
    
    /// Backward compatibility: returns intolerances as string array
    var intolerances: [String] {
        return allergiesIntolerances.map { $0.rawValue }
    }
    
    // MARK: - Firestore Codable Implementation
    
    enum CodingKeys: String, CodingKey {
        case documentID
        case userId
        case theme
        case notifications
        case lastUpdated
        case createdAt
        case familySize
        case familyMembers
        case dietaryRestrictions
        case strictExclusions
        case allergiesIntolerances
        case respectRestrictions
    }
    
    /// Initialize from Firestore decoder
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        // Handle documentID separately (managed by Firebase)
        documentID = try container.decodeIfPresent(String.self, forKey: .documentID)
        
        userId = try container.decode(String.self, forKey: .userId)
        theme = try container.decodeIfPresent(String.self, forKey: .theme) ?? "system"
        notifications = try container.decodeIfPresent(Bool.self, forKey: .notifications) ?? true
        lastUpdated = try container.decodeIfPresent(Date.self, forKey: .lastUpdated) ?? Date()
        createdAt = try container.decodeIfPresent(Date.self, forKey: .createdAt) ?? Date()
        familySize = try container.decodeIfPresent(Int.self, forKey: .familySize) ?? 2
        familyMembers = try container.decodeIfPresent([FamilyMember].self, forKey: .familyMembers) ?? []
        dietaryRestrictions = try container.decodeIfPresent([DietaryRestriction].self, forKey: .dietaryRestrictions) ?? []
        strictExclusions = try container.decodeIfPresent([StrictExclusion].self, forKey: .strictExclusions) ?? []
        allergiesIntolerances = try container.decodeIfPresent([AllergyIntolerance].self, forKey: .allergiesIntolerances) ?? []
        respectRestrictions = try container.decodeIfPresent(Bool.self, forKey: .respectRestrictions) ?? true
    }
    
    /// Encode to Firestore encoder
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        
        // documentID is handled automatically by Firebase
        try container.encodeIfPresent(documentID, forKey: .documentID)
        
        try container.encode(userId, forKey: .userId)
        try container.encode(theme, forKey: .theme)
        try container.encode(notifications, forKey: .notifications)
        try container.encode(lastUpdated, forKey: .lastUpdated)
        try container.encode(createdAt, forKey: .createdAt)
        try container.encode(familySize, forKey: .familySize)
        try container.encode(familyMembers, forKey: .familyMembers)
        try container.encode(dietaryRestrictions, forKey: .dietaryRestrictions)
        try container.encode(strictExclusions, forKey: .strictExclusions)
        try container.encode(allergiesIntolerances, forKey: .allergiesIntolerances)
        try container.encode(respectRestrictions, forKey: .respectRestrictions)
    }
    
    

    
    /// Standard initializer for creating new user preferences
    init(
        userId: String,
        theme: String = "system",
        notifications: Bool = true,
        lastUpdated: Date = Date(),
        createdAt: Date = Date(),
        familySize: Int = 2,
        familyMembers: [FamilyMember] = [],
        dietaryRestrictions: [DietaryRestriction] = [],
        strictExclusions: [StrictExclusion] = [],
        allergiesIntolerances: [AllergyIntolerance] = [],
        respectRestrictions: Bool = true
    ) {
        self.documentID = nil
        self.userId = userId
        self.theme = theme
        self.notifications = notifications
        self.lastUpdated = lastUpdated
        self.createdAt = createdAt
        self.familySize = familySize
        self.familyMembers = familyMembers
        self.dietaryRestrictions = dietaryRestrictions
        self.strictExclusions = strictExclusions
        self.allergiesIntolerances = allergiesIntolerances
        self.respectRestrictions = respectRestrictions
    }
}

// MARK: - Family Member Model

struct FamilyMember: Codable, Equatable, Identifiable, Sendable {
    let id: UUID
    var name: String
    var age: Int?
    var specialDiets: [DietaryRestriction]
    
    init(name: String, age: Int? = nil, specialDiets: [DietaryRestriction] = []) {
        self.id = UUID()
        self.name = name
        self.age = age
        self.specialDiets = specialDiets
    }
    
    // MARK: - Firestore Codable Implementation
    
    enum CodingKeys: String, CodingKey {
        case id, name, age, specialDiets
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)
        age = try container.decodeIfPresent(Int.self, forKey: .age)
        specialDiets = try container.decodeIfPresent([DietaryRestriction].self, forKey: .specialDiets) ?? []
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encodeIfPresent(age, forKey: .age)
        try container.encode(specialDiets, forKey: .specialDiets)
    }
}

// MARK: - Dietary Restrictions (Enhanced)

enum DietaryRestriction: String, CaseIterable, Codable, Sendable {
    
    // MARK: - Dietary Preferences
    case vegetarian = "Vegetarian"
    case vegan = "Vegan"
    case pescatarian = "Pescatarian"
    case keto = "Keto"
    case paleo = "Paleo"
    case lowcarb = "Low-Carb"
    case lowsodium = "Low-Sodium"
    case dairyfree = "Dairy-Free"
    case glutenfree = "Gluten-Free"
    case halal = "Halal"
    case kosher = "Kosher"
    
    // MARK: - Display Properties
    
    /// Category for grouping in UI
    var category: RestrictionCategory {
        return .dietary
    }
    
    /// Icon for UI display
    var icon: String {
        switch self {
        case .vegetarian, .vegan: return "🥬"
        case .pescatarian: return "🐟"
        case .keto, .lowcarb: return "🥩"
        case .paleo: return "🍖"
        case .lowsodium: return "🧂"
        case .dairyfree: return "🥛"
        case .glutenfree: return "🌾"
        case .halal: return "🕌"
        case .kosher: return "✡️"
        }
    }
}

// MARK: - Strict Exclusions

enum StrictExclusion: String, CaseIterable, Codable, Sendable {
    case beef = "Beef"
    case pork = "Pork"
    case chicken = "Chicken"
    case turkey = "Turkey"
    case lamb = "Lamb"
    case seafood = "Seafood"
    case fish = "Fish"
    case shellfish = "Shellfish"
    case eggs = "Eggs"
    case dairy = "Dairy Products"
    case nuts = "Tree Nuts"
    case peanuts = "Peanuts"
    case soy = "Soy Products"
    case gluten = "Gluten"
    case wheat = "Wheat"
    case corn = "Corn"
    case mushrooms = "Mushrooms"
    case onions = "Onions"
    case garlic = "Garlic"
    case spicyFood = "Spicy Food"
    case alcohol = "Alcohol"
    case caffeine = "Caffeine"
    case artificialSweeteners = "Artificial Sweeteners"
    case preservatives = "Preservatives"
    case processedFoods = "Processed Foods"
    
    var icon: String {
        switch self {
        case .beef, .pork, .chicken, .turkey, .lamb: return "🥩"
        case .seafood, .fish, .shellfish: return "🐟"
        case .eggs: return "🥚"
        case .dairy: return "🥛"
        case .nuts, .peanuts: return "🥜"
        case .soy: return "🫘"
        case .gluten, .wheat: return "🌾"
        case .corn: return "🌽"
        case .mushrooms: return "🍄"
        case .onions: return "🧅"
        case .garlic: return "🧄"
        case .spicyFood: return "🌶️"
        case .alcohol: return "🍷"
        case .caffeine: return "☕"
        default: return "🚫"
        }
    }
}

// MARK: - Allergies and Intolerances (Simplified)

enum AllergyIntolerance: String, CaseIterable, Codable, Sendable {
    // Common Food Allergies
    case nuts = "Nuts"
    case shellfish = "Shellfish"
    case dairy = "Dairy"
    case eggs = "Eggs"
    case soy = "Soy"
    case wheat = "Wheat"
    case fish = "Fish"
    case sesame = "Sesame"
    case sulfites = "Sulfites"
    case corn = "Corn"
    case chocolate = "Chocolate"
    case tomatoes = "Tomatoes"
    case citrus = "Citrus"
    case mushrooms = "Mushrooms"
    case onions = "Onions"
    case garlic = "Garlic"
    case spices = "Spices"
    case caffeine = "Caffeine"
    case alcohol = "Alcohol"
    case artificial_sweeteners = "Artificial Sweeteners"
    
    var icon: String {
        switch self {
        case .nuts: return "🥜"
        case .shellfish: return "🦐"
        case .dairy: return "🥛"
        case .eggs: return "🥚"
        case .soy: return "🫘"
        case .wheat: return "🌾"
        case .fish: return "🐟"
        case .sesame: return "🫘"
        case .sulfites: return "🍷"
        case .corn: return "🌽"
        case .chocolate: return "🍫"
        case .tomatoes: return "🍅"
        case .citrus: return "🍊"
        case .mushrooms: return "🍄"
        case .onions: return "🧅"
        case .garlic: return "🧄"
        case .spices: return "🌶️"
        case .caffeine: return "☕"
        case .alcohol: return "🍷"
        case .artificial_sweeteners: return "🧪"
        }
    }
}

// MARK: - Restriction Categories (Enhanced)

enum RestrictionCategory: String, CaseIterable, Sendable {
    case dietary = "Dietary Restrictions"
    case strictExclusions = "Strict Exclusions"
    case allergies = "Allergies & Intolerances"
    case family = "Family Information"
    
    var icon: String {
        switch self {
        case .dietary: return "🥗"
        case .strictExclusions: return "🚫"
        case .allergies: return "⚠️"
        case .family: return "👨‍👩‍👧‍👦"
        }
    }
    
    var description: String {
        switch self {
        case .dietary: return "Foods you prefer to eat or avoid"
        case .strictExclusions: return "Foods you never want to see in recipes"
        case .allergies: return "Medical allergies and food intolerances"
        case .family: return "Information about household members"
        }
    }
}

// MARK: - Default Values & Firestore Helpers

extension UserPreferences {
    
    /// Default preferences for new users - Firestore compatible
    static func createDefault(for userId: String) -> UserPreferences {
        return UserPreferences(
            userId: userId,
            theme: "system",
            notifications: true,
            lastUpdated: Date(),
            createdAt: Date(),
            familySize: 2,
            familyMembers: [],
            dietaryRestrictions: [],
            strictExclusions: [],
            allergiesIntolerances: [],
            respectRestrictions: true
        )
    }
    
    /// Legacy default preferences (for backward compatibility)
    @MainActor
    static let `default` = UserPreferences(
        userId: "",
        theme: "system",
        notifications: true,
        lastUpdated: Date(),
        createdAt: Date(),
        familySize: 2,
        familyMembers: [],
        dietaryRestrictions: [],
        strictExclusions: [],
        allergiesIntolerances: [],
        respectRestrictions: true
    )
    
    /// Sample preferences for testing/preview - Firestore compatible
    @MainActor
    static let sample = UserPreferences(
        userId: "sample_user_123",
        theme: "dark",
        notifications: false,
        lastUpdated: Date(),
        createdAt: Date(),
        familySize: 4,
        familyMembers: [
            FamilyMember(name: "John", age: 35),
            FamilyMember(name: "Jane", age: 32),
            FamilyMember(name: "Emma", age: 8, specialDiets: [.vegetarian]),
            FamilyMember(name: "Liam", age: 5)
        ],
        dietaryRestrictions: [.vegetarian, .lowsodium],
        strictExclusions: [.pork, .beef],
        allergiesIntolerances: [.nuts, .dairy],
        respectRestrictions: true
    )
    
    /// Update the lastUpdated timestamp to current time
    mutating func updateTimestamp() {
        lastUpdated = Date()
    }
    
    /// Validate that all required fields are present for Firestore
    func isValidForFirestore() -> Bool {
        return !userId.isEmpty
    }
    
    /// Create a copy with updated timestamp
    func withUpdatedTimestamp() -> UserPreferences {
        var updated = self
        updated.lastUpdated = Date()
        return updated
    }
}
