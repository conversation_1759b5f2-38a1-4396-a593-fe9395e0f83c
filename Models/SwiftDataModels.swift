import Foundation
import SwiftData
import UIKit

// MARK: - SwiftData Models for Local Storage

/// Saved recipe model for SwiftData persistence
@Model
final class SavedRecipe {
    var id: UUID
    var name: String
    var ingredients: [String]
    var instructions: String
    var imageData: Data?
    var dateAdded: Date
    var cookingTime: Int
    var servings: Int
    var difficulty: String
    var tags: [String]
    var isFavorite: Bool
    
    init(id: UUID = UUID(), 
         name: String, 
         ingredients: [String], 
         instructions: String, 
         imageData: Data? = nil,
         cookingTime: Int = 30,
         servings: Int = 2,
         difficulty: String = "medium",
         tags: [String] = [],
         isFavorite: Bool = false) {
        self.id = id
        self.name = name
        self.ingredients = ingredients
        self.instructions = instructions
        self.imageData = imageData
        self.dateAdded = Date()
        self.cookingTime = cookingTime
        self.servings = servings
        self.difficulty = difficulty
        self.tags = tags
        self.isFavorite = isFavorite
    }
}

/// Saved ingredient model for SwiftData persistence (pantry items)
@Model
final class SavedIngredient {
    var id: UUID
    var name: String
    var category: String
    var dateAdded: Date
    var expiryDate: Date?
    var quantity: String?
    var notes: String?
    var isRecent: Bool
    
    init(id: UUID = UUID(),
         name: String,
         category: String = "other",
         dateAdded: Date = Date(),
         expiryDate: Date? = nil,
         quantity: String? = nil,
         notes: String? = nil,
         isRecent: Bool = false) {
        self.id = id
        self.name = name
        self.category = category
        self.dateAdded = dateAdded
        self.expiryDate = expiryDate
        self.quantity = quantity
        self.notes = notes
        self.isRecent = isRecent
    }
}

/// User preferences model for SwiftData persistence
@Model
final class SavedUserPreferences {
    var id: UUID
    var userId: String
    var theme: String
    var notifications: Bool
    var lastUpdated: Date
    var dietaryRestrictions: [String] // Stored as string array for simplicity
    var familySize: Int
    var respectRestrictions: Bool
    var language: String
    
    // New fields for enhanced preferences
    var familyMembersData: Data? // JSON encoded FamilyMember array
    var strictExclusions: [String] // StrictExclusion enum values
    var allergiesAndIntolerances: [String] // AllergyIntolerance enum values
    
    init(id: UUID = UUID(),
         userId: String = "",
         theme: String = "system",
         notifications: Bool = true,
         dietaryRestrictions: [String] = [],
         familySize: Int = 2,
         respectRestrictions: Bool = true,
         language: String = "en",
         familyMembersData: Data? = nil,
         strictExclusions: [String] = [],
         allergiesAndIntolerances: [String] = []) {
        self.id = id
        self.userId = userId
        self.theme = theme
        self.notifications = notifications
        self.lastUpdated = Date()
        self.dietaryRestrictions = dietaryRestrictions
        self.familySize = familySize
        self.respectRestrictions = respectRestrictions
        self.language = language
        self.familyMembersData = familyMembersData
        self.strictExclusions = strictExclusions
        self.allergiesAndIntolerances = allergiesAndIntolerances
    }
    
    /// Convert to UserPreferences struct for compatibility
    func toUserPreferences() -> UserPreferences {
        var preferences = UserPreferences.createDefault(for: userId)
        preferences.userId = userId
        preferences.theme = theme
        preferences.notifications = notifications
        preferences.lastUpdated = lastUpdated
        preferences.familySize = familySize
        preferences.respectRestrictions = respectRestrictions
        
        // Convert string arrays back to enum values
        preferences.dietaryRestrictions = dietaryRestrictions.compactMap { DietaryRestriction(rawValue: $0) }
        preferences.strictExclusions = strictExclusions.compactMap { StrictExclusion(rawValue: $0) }
        preferences.allergiesIntolerances = allergiesAndIntolerances.compactMap { AllergyIntolerance(rawValue: $0) }
        
        // Decode family members from JSON data
        if let familyMembersData = familyMembersData,
           let familyMembers = try? JSONDecoder().decode([FamilyMember].self, from: familyMembersData) {
            preferences.familyMembers = familyMembers
        }
        
        return preferences
    }
    
    /// Create from UserPreferences struct
    static func from(_ preferences: UserPreferences) -> SavedUserPreferences {
        // Encode family members to JSON data
        let familyMembersData = try? JSONEncoder().encode(preferences.familyMembers)
        
        return SavedUserPreferences(
            userId: preferences.userId,
            theme: preferences.theme,
            notifications: preferences.notifications,
            dietaryRestrictions: preferences.dietaryRestrictions.map { $0.rawValue },
            familySize: preferences.familySize,
            respectRestrictions: preferences.respectRestrictions,
            familyMembersData: familyMembersData,
            strictExclusions: preferences.strictExclusions.map { $0.rawValue },
            allergiesAndIntolerances: preferences.allergiesIntolerances.map { $0.rawValue }
        )
    }
}

/// Shopping list item model for SwiftData persistence
@Model
final class ShoppingListItem {
    var id: UUID
    var name: String
    var category: String
    var quantity: String?
    var isCompleted: Bool
    var dateAdded: Date
    var priority: Int
    
    init(id: UUID = UUID(),
         name: String,
         category: String = "other",
         quantity: String? = nil,
         isCompleted: Bool = false,
         priority: Int = 1) {
        self.id = id
        self.name = name
        self.category = category
        self.quantity = quantity
        self.isCompleted = isCompleted
        self.dateAdded = Date()
        self.priority = priority
    }
}

/// Recipe history model for tracking generated recipes
@Model
final class RecipeHistory {
    var id: UUID
    var recipeName: String
    var ingredients: [String]
    var generatedAt: Date
    var wasUsed: Bool
    var rating: Int?
    var notes: String?
    
    init(id: UUID = UUID(),
         recipeName: String,
         ingredients: [String],
         wasUsed: Bool = false,
         rating: Int? = nil,
         notes: String? = nil) {
        self.id = id
        self.recipeName = recipeName
        self.ingredients = ingredients
        self.generatedAt = Date()
        self.wasUsed = wasUsed
        self.rating = rating
        self.notes = notes
    }
} 