import Foundation
import SwiftUI

struct Ingredient: Identifiable, <PERSON><PERSON>ble, <PERSON><PERSON><PERSON>, Sendable {
    let id: UUID
    var name: String
    var category: PantryCategory
    var isSelected: Bool = true
    var dateAdded: Date = Date()
    
    /// Initialize a new ingredient with a generated UUID
    init(name: String, category: PantryCategory, dateAdded: Date = Date()) {
        self.id = UUID()
        self.name = name
        self.category = category
        self.dateAdded = dateAdded
    }

    /// Initialize an ingredient using a pre-existing persistent ID
    init(id: UUID, name: String, category: PantryCategory, dateAdded: Date = Date()) {
        self.id = id
        self.name = name
        self.category = category
        self.dateAdded = dateAdded
    }
}

// MARK: - Shared Error Types for iOS 17 Modernization

/// Shared cancellation error for .task(id:) modernization across all ViewModels
struct TaskCancellationError: LocalizedError, Sendable {
    var errorDescription: String? {
        return "Task was cancelled"
    }
} 