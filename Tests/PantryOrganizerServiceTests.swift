import XCTest
@testable import IngredientScanner

final class PantryOrganizerServiceTests: XCTestCase {
    var organizer: PantryOrganizerService!
    var mockGeminiService: MockGeminiService!
    var mockPantryService: MockPantryService!
    
    override func setUp() {
        super.setUp()
        mockGeminiService = MockGeminiService()
        mockPantryService = MockPantryService()
        organizer = PantryOrganizerService(
            geminiService: mockGeminiService,
            pantryService: mockPantryService
        )
    }
    
    override func tearDown() {
        organizer = nil
        mockGeminiService = nil
        mockPantryService = nil
        super.tearDown()
    }
    
    // MARK: - Basic Functionality Tests
    
    func testOrganizeWithValidInput() async throws {
        // Given
        let items = createTestIngredients()
        let categories = ["Produce", "Dairy", "Meat"]
        let expectedResponse = createValidGeminiResponse()
        mockGeminiService.mockResponse = expectedResponse
        
        // When
        let plan = try await organizer.organize(items: items, allowedCategories: categories)
        
        // Then
        XCTAssertFalse(plan.updates.isEmpty, "Plan should contain updates")
        XCTAssertTrue(mockGeminiService.callGeminiAPICalled, "Should call Gemini API")
    }
    
    func testOrganizeWithEmptyItems() async {
        // Given
        let items: [Ingredient] = []
        let categories = ["Produce"]
        
        // When/Then
        do {
            _ = try await organizer.organize(items: items, allowedCategories: categories)
            XCTFail("Should throw error for empty items")
        } catch OrganizerError.invalidResponse(let message) {
            XCTAssertEqual(message, "No items to organize")
        } catch {
            XCTFail("Wrong error type: \(error)")
        }
    }
    
    func testOrganizeWithEmptyCategories() async {
        // Given
        let items = createTestIngredients()
        let categories: [String] = []
        
        // When/Then
        do {
            _ = try await organizer.organize(items: items, allowedCategories: categories)
            XCTFail("Should throw error for empty categories")
        } catch OrganizerError.invalidResponse(let message) {
            XCTAssertEqual(message, "No allowed categories provided")
        } catch {
            XCTFail("Wrong error type: \(error)")
        }
    }
    
    // MARK: - Batch Processing Tests
    
    func testBatchProcessingWithLargeInput() async throws {
        // Given
        let items = createLargeTestSet(count: 25) // Should create 3 batches with default size 10
        let categories = ["Produce", "Dairy"]
        mockGeminiService.mockResponse = createValidGeminiResponse()
        
        // When
        let plan = try await organizer.organize(items: items, allowedCategories: categories)
        
        // Then
        XCTAssertGreaterThanOrEqual(mockGeminiService.callCount, 3, "Should make multiple API calls for batches")
        XCTAssertFalse(plan.updates.isEmpty, "Should have consolidated results")
    }
    
    func testPartialBatchFailure() async {
        // Given
        let items = createLargeTestSet(count: 20)
        let categories = ["Produce"]
        mockGeminiService.shouldFailOnSecondCall = true
        mockGeminiService.mockResponse = createValidGeminiResponse()
        
        // When/Then
        do {
            _ = try await organizer.organize(items: items, allowedCategories: categories)
            XCTFail("Should throw partial batch success error")
        } catch OrganizerError.partialBatchSuccess(let processed, let total) {
            XCTAssertGreaterThan(processed, 0, "Should have processed some items")
            XCTAssertEqual(total, 20, "Should report correct total")
        } catch {
            XCTFail("Wrong error type: \(error)")
        }
    }
    
    // MARK: - Apply Plan Tests
    
    func testApplyPlanWithUpdates() async throws {
        // Given
        let plan = createTestCleanUpPlan()
        mockPantryService.setupMockIngredients(createTestIngredients())
        
        // When
        let summary = try await organizer.apply(plan: plan)
        
        // Then
        XCTAssertEqual(summary.updatedCount, plan.updates.count)
        XCTAssertEqual(summary.removedCount, plan.removals.count)
        XCTAssertEqual(summary.mergedCount, plan.merges.reduce(0) { $0 + $1.loserIds.count })
    }
    
    func testApplyEmptyPlan() async throws {
        // Given
        let plan = CleanUpPlan(updates: [], removals: [], merges: [])
        
        // When
        let summary = try await organizer.apply(plan: plan)
        
        // Then
        XCTAssertEqual(summary.updatedCount, 0)
        XCTAssertEqual(summary.removedCount, 0)
        XCTAssertEqual(summary.mergedCount, 0)
    }
    
    // MARK: - Error Handling Tests
    
    func testGeminiAPIFailure() async {
        // Given
        let items = createTestIngredients()
        let categories = ["Produce"]
        mockGeminiService.shouldThrowError = true
        
        // When/Then
        do {
            _ = try await organizer.organize(items: items, allowedCategories: categories)
            XCTFail("Should propagate Gemini API error")
        } catch {
            // Expected - should propagate the error
        }
    }
    
    func testInvalidGeminiResponse() async {
        // Given
        let items = createTestIngredients()
        let categories = ["Produce"]
        mockGeminiService.mockResponse = "Invalid JSON response"
        
        // When/Then
        do {
            _ = try await organizer.organize(items: items, allowedCategories: categories)
            XCTFail("Should throw parse error for invalid response")
        } catch OrganizerError.parseError {
            // Expected
        } catch {
            XCTFail("Wrong error type: \(error)")
        }
    }
    
    // MARK: - Helper Methods
    
    private func createTestIngredients() -> [Ingredient] {
        return [
            Ingredient(id: UUID(), name: "Apples", category: .produce, quantity: "5", unit: "pieces"),
            Ingredient(id: UUID(), name: "Milk", category: .dairy, quantity: "1", unit: "gallon"),
            Ingredient(id: UUID(), name: "Chicken", category: .protein, quantity: "2", unit: "lbs")
        ]
    }
    
    private func createLargeTestSet(count: Int) -> [Ingredient] {
        return (0..<count).map { index in
            Ingredient(id: UUID(), name: "Item \(index)", category: .produce, quantity: "1", unit: "piece")
        }
    }
    
    private func createValidGeminiResponse() -> String {
        return """
        {
            "updates": [
                {
                    "id": "\(UUID().uuidString)",
                    "newName": "Fresh Apples",
                    "newCategory": "Produce"
                }
            ],
            "removals": [],
            "merges": []
        }
        """
    }
    
    private func createTestCleanUpPlan() -> CleanUpPlan {
        return CleanUpPlan(
            updates: [UpdateOp(id: UUID(), newName: "Updated Item", newCategory: "Produce")],
            removals: [UUID()],
            merges: [MergeOp(winnerId: UUID(), loserIds: [UUID()])]
        )
    }
}

// MARK: - Mock Classes

class MockGeminiService: GeminiAPIService {
    var mockResponse: String = ""
    var shouldThrowError = false
    var shouldFailOnSecondCall = false
    var callCount = 0
    var callGeminiAPICalled = false
    
    override func callGeminiAPI(prompt: String) async throws -> String {
        callGeminiAPICalled = true
        callCount += 1
        
        if shouldThrowError {
            throw NSError(domain: "MockError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Mock API failure"])
        }
        
        if shouldFailOnSecondCall && callCount == 2 {
            throw NSError(domain: "MockError", code: 2, userInfo: [NSLocalizedDescriptionKey: "Second call failure"])
        }
        
        return mockResponse
    }
}

class MockPantryService: PantryService {
    private var mockIngredients: [Ingredient] = []
    
    func setupMockIngredients(_ ingredients: [Ingredient]) {
        mockIngredients = ingredients
    }
    
    override func updateIngredientById(_ id: UUID, newName: String, newCategory: PantryCategory) async {
        // Mock implementation
    }
    
    override func removeIngredientById(_ id: UUID) async {
        // Mock implementation
    }
    
    override func mergeIngredients(winnerId: UUID, loserIds: [UUID]) async {
        // Mock implementation
    }
} 