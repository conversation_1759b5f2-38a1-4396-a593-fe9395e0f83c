import XCTest
import Swift<PERSON>
@testable import IngredientScanner

/// Regression tests for food preferences save functionality
/// 
/// These tests specifically target the bug where dietary restrictions and allergies
/// were not being saved properly when added through the AddItemSheet.
/// 
/// Bug Report: Users reported that adding items to dietary restrictions or allergies
/// would not persist - items would disappear when returning to the preferences screen.
@MainActor
final class FoodPreferencesRegressionTests: XCTestCase {
    
    var mockProfileService: MockUserProfileService!
    var mockAppPreferences: MockAppPreferences!
    
    override func setUp() {
        super.setUp()
        mockProfileService = MockUserProfileService()
        mockAppPreferences = MockAppPreferences()
    }
    
    override func tearDown() {
        mockProfileService = nil
        mockAppPreferences = nil
        super.tearDown()
    }
    
    // MARK: - Regression Test for Allergy Addition Bug
    
    /// Test that adding allergies through AddItemSheet properly updates local state
    /// and enables the Save button for persistence
    func testAllergyAdditionUpdatesStateAndEnablesSave() async throws {
        // Given: A user profile with no existing allergies
        let initialProfile = UserProfile(id: "test-user")
        mockProfileService.userProfile = initialProfile
        
        // When: User adds a new allergy through the UI flow
        let foodPreferencesView = FoodPreferencesView()
            .environmentObject(mockProfileService)
            .environmentObject(mockAppPreferences)
        
        // Simulate the AddItemSheet onAdd callback
        var testAllergies: [String] = []
        let newAllergy = "peanuts"
        
        // This simulates the exact logic from AddItemSheet
        let trimmedAllergy = newAllergy.trimmingCharacters(in: .whitespacesAndNewlines).lowercased()
        if !trimmedAllergy.isEmpty && !testAllergies.contains(trimmedAllergy) {
            testAllergies.append(trimmedAllergy)
        }
        
        // Then: The allergy should be added to local state
        XCTAssertEqual(testAllergies.count, 1)
        XCTAssertEqual(testAllergies.first, "peanuts")
        
        // And: The hasChanges computed property should detect the change
        let hasChanges = testAllergies != initialProfile.allergies
        XCTAssertTrue(hasChanges, "hasChanges should be true when allergies are modified")
    }
    
    /// Test that dietary restrictions addition works correctly
    func testDietaryRestrictionAdditionUpdatesState() async throws {
        // Given: A user profile with no existing dietary restrictions
        let initialProfile = UserProfile(id: "test-user")
        mockProfileService.userProfile = initialProfile
        
        // When: User selects a dietary restriction
        var selectedRestrictions: Set<DietaryRestriction> = []
        let newRestriction = DietaryRestriction.vegetarian
        
        // Simulate the toggle logic
        if selectedRestrictions.contains(newRestriction) {
            selectedRestrictions.remove(newRestriction)
        } else {
            selectedRestrictions.insert(newRestriction)
        }
        
        // Then: The restriction should be added
        XCTAssertTrue(selectedRestrictions.contains(newRestriction))
        
        // And: Changes should be detected
        let hasChanges = selectedRestrictions != Set(initialProfile.dietaryRestrictions)
        XCTAssertTrue(hasChanges)
    }
    
    /// Test that strict exclusions addition works correctly
    func testStrictExclusionAdditionUpdatesState() async throws {
        // Given: A user profile with no existing strict exclusions
        let initialProfile = UserProfile(id: "test-user")
        mockProfileService.userProfile = initialProfile
        
        // When: User adds a strict exclusion
        var testExclusions: [String] = []
        let newExclusion = "pork"
        
        // Simulate the AddItemSheet logic for exclusions
        let trimmedExclusion = newExclusion.trimmingCharacters(in: .whitespacesAndNewlines).lowercased()
        if !trimmedExclusion.isEmpty && !testExclusions.contains(trimmedExclusion) {
            testExclusions.append(trimmedExclusion)
        }
        
        // Then: The exclusion should be added
        XCTAssertEqual(testExclusions.count, 1)
        XCTAssertEqual(testExclusions.first, "pork")
        
        // And: Changes should be detected
        let hasChanges = testExclusions != initialProfile.strictExclusions
        XCTAssertTrue(hasChanges)
    }
    
    // MARK: - Integration Tests
    
    /// Test the complete save flow to ensure data persists
    func testCompleteSaveFlowPersistsData() async throws {
        // Given: A user profile and mock service
        let userId = "test-user"
        let initialProfile = UserProfile(id: userId)
        mockProfileService.userProfile = initialProfile
        
        // When: User adds allergies and saves
        let newAllergies = ["peanuts", "shellfish"]
        let newRestrictions = [DietaryRestriction.vegetarian, DietaryRestriction.glutenFree]
        let newExclusions = ["pork", "alcohol"]
        
        // Simulate the save operation
        try await mockProfileService.updateFoodPreferences(
            userId: userId,
            strictExclusions: newExclusions,
            dietaryRestrictions: newRestrictions,
            allergies: newAllergies
        )
        
        // Then: The mock service should have received the update
        XCTAssertTrue(mockProfileService.updateFoodPreferencesCalled)
        XCTAssertEqual(mockProfileService.lastUpdatedAllergies, newAllergies)
        XCTAssertEqual(mockProfileService.lastUpdatedRestrictions, newRestrictions)
        XCTAssertEqual(mockProfileService.lastUpdatedExclusions, newExclusions)
    }
    
    /// Test that the onAppear initialization logic doesn't overwrite user changes
    func testOnAppearDoesNotOverwriteUserChanges() async throws {
        // Given: A view that has been initialized
        var hasInitialized = false
        var localAllergies = ["peanuts"] // User has made changes
        
        let profile = UserProfile(id: "test-user") // Profile has no allergies
        
        // When: onAppear is called again (simulating navigation back)
        // This simulates the fixed logic
        if !hasInitialized {
            localAllergies = profile.allergies // Should only happen once
            hasInitialized = true
        }
        
        // Then: User changes should be preserved
        XCTAssertEqual(localAllergies, ["peanuts"], "User changes should not be overwritten")
        XCTAssertTrue(hasInitialized, "Initialization flag should be set")
        
        // When: onAppear is called again
        let previousAllergies = localAllergies
        if !hasInitialized {
            localAllergies = profile.allergies
            hasInitialized = true
        }
        
        // Then: Changes should still be preserved
        XCTAssertEqual(localAllergies, previousAllergies, "Subsequent onAppear calls should not reset state")
    }
}

// MARK: - Mock Classes

class MockUserProfileService: UserProfileService {
    var updateFoodPreferencesCalled = false
    var lastUpdatedAllergies: [String]?
    var lastUpdatedRestrictions: [DietaryRestriction]?
    var lastUpdatedExclusions: [String]?
    
    override func updateFoodPreferences(
        userId: String,
        strictExclusions: [String]? = nil,
        dietaryRestrictions: [DietaryRestriction]? = nil,
        allergies: [String]? = nil
    ) async throws {
        updateFoodPreferencesCalled = true
        lastUpdatedAllergies = allergies
        lastUpdatedRestrictions = dietaryRestrictions
        lastUpdatedExclusions = strictExclusions
        
        // Simulate successful update
        if var profile = userProfile {
            if let allergies = allergies {
                profile.allergies = allergies
            }
            if let restrictions = dietaryRestrictions {
                profile.dietaryRestrictions = restrictions
            }
            if let exclusions = strictExclusions {
                profile.strictExclusions = exclusions
            }
            userProfile = profile
        }
    }
}

class MockAppPreferences: AppPreferences {
    override init() {
        super.init()
        self.fontSize = .medium
        self.language = .english
    }
}
