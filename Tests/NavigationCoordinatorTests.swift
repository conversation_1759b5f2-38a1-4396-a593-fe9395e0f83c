import XCTest
import Swift<PERSON>
@testable import IngredientScanner

@MainActor
final class NavigationCoordinatorTests: XCTestCase {
    
    var coordinator: NavigationCoordinator!
    
    override func setUp() {
        super.setUp()
        coordinator = NavigationCoordinator()
    }
    
    override func tearDown() {
        coordinator = nil
        super.tearDown()
    }
    
    // MARK: - Initialization Tests
    
    func testInitialState() {
        XCTAssertEqual(coordinator.navigationDepth, 0)
        XCTAssertEqual(coordinator.tabSelection, 0)
        XCTAssertTrue(coordinator.isAtRoot)
        XCTAssertFalse(coordinator.canNavigateBack)
    }
    
    // MARK: - Core Navigation Tests
    
    func testNavigateToRoute() {
        coordinator.navigate(to: .staging)
        
        XCTAssertEqual(coordinator.navigationDepth, 1)
        XCTAssertFalse(coordinator.isAtRoot)
        XCTAssertTrue(coordinator.canNavigateBack)
    }
    
    func testNavigateMultipleRoutes() {
        coordinator.navigate(to: .staging)
        coordinator.navigate(to: .profile)
        coordinator.navigate(to: .signIn)
        
        XCTAssertEqual(coordinator.navigationDepth, 3)
        XCTAssertFalse(coordinator.isAtRoot)
        XCTAssertTrue(coordinator.canNavigateBack)
    }
    
    func testPopToRoot() {
        coordinator.navigate(to: .staging)
        coordinator.navigate(to: .profile)
        coordinator.navigate(to: .signIn)
        
        coordinator.popToRoot()
        
        XCTAssertEqual(coordinator.navigationDepth, 0)
        XCTAssertTrue(coordinator.isAtRoot)
        XCTAssertFalse(coordinator.canNavigateBack)
    }
    
    func testPopToRootWhenEmpty() {
        coordinator.popToRoot()
        
        XCTAssertEqual(coordinator.navigationDepth, 0)
        XCTAssertTrue(coordinator.isAtRoot)
        XCTAssertFalse(coordinator.canNavigateBack)
    }
    
    func testPopLevels() {
        coordinator.navigate(to: .staging)
        coordinator.navigate(to: .profile)
        coordinator.navigate(to: .signIn)
        coordinator.navigate(to: .preferencesEdit)
        
        coordinator.popLevels(2)
        
        XCTAssertEqual(coordinator.navigationDepth, 2)
        XCTAssertFalse(coordinator.isAtRoot)
        XCTAssertTrue(coordinator.canNavigateBack)
    }
    
    func testPopLevelsWithInvalidCount() {
        coordinator.navigate(to: .staging)
        coordinator.navigate(to: .profile)
        
        // Try to pop more levels than exist
        coordinator.popLevels(5)
        
        // Should remain unchanged
        XCTAssertEqual(coordinator.navigationDepth, 2)
    }
    
    func testPopLevelsWithZeroCount() {
        coordinator.navigate(to: .staging)
        coordinator.navigate(to: .profile)
        
        coordinator.popLevels(0)
        
        // Should remain unchanged
        XCTAssertEqual(coordinator.navigationDepth, 2)
    }
    
    func testPopBack() {
        coordinator.navigate(to: .staging)
        coordinator.navigate(to: .profile)
        
        coordinator.popBack()
        
        XCTAssertEqual(coordinator.navigationDepth, 1)
        XCTAssertFalse(coordinator.isAtRoot)
        XCTAssertTrue(coordinator.canNavigateBack)
    }
    
    func testPopBackWhenEmpty() {
        coordinator.popBack()
        
        XCTAssertEqual(coordinator.navigationDepth, 0)
        XCTAssertTrue(coordinator.isAtRoot)
        XCTAssertFalse(coordinator.canNavigateBack)
    }
    
    func testReplaceCurrent() {
        coordinator.navigate(to: .staging)
        coordinator.navigate(to: .profile)
        
        coordinator.replaceCurrent(with: .signIn)
        
        XCTAssertEqual(coordinator.navigationDepth, 2)
        XCTAssertFalse(coordinator.isAtRoot)
        XCTAssertTrue(coordinator.canNavigateBack)
    }
    
    func testReplaceCurrentWhenEmpty() {
        coordinator.replaceCurrent(with: .staging)
        
        XCTAssertEqual(coordinator.navigationDepth, 1)
        XCTAssertFalse(coordinator.isAtRoot)
        XCTAssertTrue(coordinator.canNavigateBack)
    }
    
    func testClearPath() {
        coordinator.navigate(to: .staging)
        coordinator.navigate(to: .profile)
        coordinator.navigate(to: .signIn)
        
        coordinator.clearPath()
        
        XCTAssertEqual(coordinator.navigationDepth, 0)
        XCTAssertTrue(coordinator.isAtRoot)
        XCTAssertFalse(coordinator.canNavigateBack)
    }
    
    // MARK: - Convenience Navigation Tests
    
    func testNavigateToStaging() {
        coordinator.navigateToStaging()
        
        XCTAssertEqual(coordinator.navigationDepth, 1)
        XCTAssertFalse(coordinator.isAtRoot)
    }
    
    func testNavigateToBatchProcessing() {
        let images = [UIImage()]
        coordinator.navigateToBatchProcessing(images: images)
        
        XCTAssertEqual(coordinator.navigationDepth, 1)
        XCTAssertFalse(coordinator.isAtRoot)
    }
    
    func testNavigateToBatchVisionResults() {
        let images = [UIImage()]
        let responses = ["test response"]
        coordinator.navigateToBatchVisionResults(images: images, visionResponses: responses)
        
        XCTAssertEqual(coordinator.navigationDepth, 1)
        XCTAssertFalse(coordinator.isAtRoot)
    }
    
    func testNavigateToBatchGeminiProcessing() {
        let images = [UIImage()]
        let responses = ["test response"]
        coordinator.navigateToBatchGeminiProcessing(images: images, visionResponses: responses)
        
        XCTAssertEqual(coordinator.navigationDepth, 1)
        XCTAssertFalse(coordinator.isAtRoot)
    }
    
    func testNavigateToResults() {
        let ingredients = [Ingredient(id: UUID(), name: "Test", category: .produce, confidence: 0.9)]
        coordinator.navigateToResults(ingredients: ingredients)
        
        XCTAssertEqual(coordinator.navigationDepth, 1)
        XCTAssertFalse(coordinator.isAtRoot)
    }
    
    #if DEBUG
    func testNavigateToDebug() {
        let ingredients = [Ingredient(id: UUID(), name: "Test", category: .produce, confidence: 0.9)]
        coordinator.navigateToDebug(visionResponse: "vision", geminiResponse: "gemini", ingredients: ingredients)
        
        XCTAssertEqual(coordinator.navigationDepth, 1)
        XCTAssertFalse(coordinator.isAtRoot)
    }
    #endif
    
    func testNavigateToProfile() {
        coordinator.navigateToProfile()
        
        XCTAssertEqual(coordinator.navigationDepth, 1)
        XCTAssertFalse(coordinator.isAtRoot)
    }
    
    func testNavigateToSignIn() {
        coordinator.navigateToSignIn()
        
        XCTAssertEqual(coordinator.navigationDepth, 1)
        XCTAssertFalse(coordinator.isAtRoot)
    }
    
    func testNavigateToPreferencesEdit() {
        coordinator.navigateToPreferencesEdit()
        
        XCTAssertEqual(coordinator.navigationDepth, 1)
        XCTAssertFalse(coordinator.isAtRoot)
    }
    
    func testNavigateToRecipeGenerator() {
        coordinator.navigateToRecipeGenerator()
        
        XCTAssertEqual(coordinator.navigationDepth, 1)
        XCTAssertFalse(coordinator.isAtRoot)
    }
    
    func testNavigateToRecipeDetail() {
        let recipe = Recipe(id: UUID(), title: "Test Recipe", ingredients: [], instructions: [], cookingTime: 30, servings: 4, difficulty: .easy, category: .dinner)
        coordinator.navigateToRecipeDetail(recipe: recipe)
        
        XCTAssertEqual(coordinator.navigationDepth, 1)
        XCTAssertFalse(coordinator.isAtRoot)
    }
    
    func testNavigateToIngredientsView() {
        let recipe = Recipe(id: UUID(), title: "Test Recipe", ingredients: [], instructions: [], cookingTime: 30, servings: 4, difficulty: .easy, category: .dinner)
        coordinator.navigateToIngredientsView(recipe: recipe)
        
        XCTAssertEqual(coordinator.navigationDepth, 1)
        XCTAssertFalse(coordinator.isAtRoot)
    }
    
    // Settings flow removed; navigation to settings no longer supported

    // MARK: - Tab Management Tests
    
    func testSwitchToTab() {
        coordinator.switchToTab(2)
        
        XCTAssertEqual(coordinator.tabSelection, 2)
    }
    
    func testSwitchToTabWithInvalidIndex() {
        coordinator.switchToTab(-1)
        XCTAssertEqual(coordinator.tabSelection, 0) // Should remain unchanged
        
        coordinator.switchToTab(10)
        XCTAssertEqual(coordinator.tabSelection, 0) // Should remain unchanged
    }
    
    func testSwitchToPantryTab() {
        coordinator.switchToPantryTab()
        
        XCTAssertEqual(coordinator.tabSelection, 1)
    }
    
    func testSwitchToProfileTab() {
        coordinator.switchToProfileTab()
        
        XCTAssertEqual(coordinator.tabSelection, 3)
    }
    
    func testSwitchToPantryTabAndResetScan() async {
        coordinator.navigate(to: .staging)
        coordinator.navigate(to: .profile)
        
        coordinator.switchToPantryTabAndResetScan()
        
        XCTAssertEqual(coordinator.tabSelection, 1)
        
        // Wait for async reset to complete
        try? await Task.sleep(nanoseconds: 600_000_000) // 0.6 seconds
        
        XCTAssertEqual(coordinator.navigationDepth, 0)
        XCTAssertTrue(coordinator.isAtRoot)
    }
    
    func testResetScanTab() {
        coordinator.navigate(to: .staging)
        coordinator.navigate(to: .profile)
        
        coordinator.resetScanTab()
        
        XCTAssertEqual(coordinator.navigationDepth, 1) // Should have staging
        XCTAssertFalse(coordinator.isAtRoot)
    }
    
    // MARK: - Deep Linking Tests
    
    func testHandleDeepLinkStaging() {
        let url = URL(string: "ingredientscanner://staging")!
        
        coordinator.handleDeepLink(url)
        
        XCTAssertEqual(coordinator.navigationDepth, 1)
        XCTAssertEqual(coordinator.tabSelection, 0)
    }
    
    func testHandleDeepLinkProfile() {
        let url = URL(string: "ingredientscanner://profile")!
        
        coordinator.handleDeepLink(url)
        
        XCTAssertEqual(coordinator.navigationDepth, 1)
        XCTAssertEqual(coordinator.tabSelection, 3)
    }
    
    func testHandleDeepLinkRecipes() {
        let url = URL(string: "ingredientscanner://recipes")!
        
        coordinator.handleDeepLink(url)
        
        XCTAssertEqual(coordinator.navigationDepth, 1)
        XCTAssertEqual(coordinator.tabSelection, 2)
    }
    
    // Settings deep link removed; no longer supported

    func testHandleDeepLinkUnknownPath() {
        let url = URL(string: "ingredientscanner://unknown")!
        
        coordinator.handleDeepLink(url)
        
        XCTAssertEqual(coordinator.navigationDepth, 1) // Should default to staging
        XCTAssertEqual(coordinator.tabSelection, 0)
    }
    
    func testHandleDeepLinkInvalidScheme() {
        let url = URL(string: "invalid://staging")!
        
        coordinator.handleDeepLink(url)
        
        XCTAssertEqual(coordinator.navigationDepth, 0) // Should remain unchanged
        XCTAssertEqual(coordinator.tabSelection, 0)
    }
    
    func testHandleDeepLinkInvalidURL() {
        let url = URL(string: "not-a-url")!
        
        coordinator.handleDeepLink(url)
        
        XCTAssertEqual(coordinator.navigationDepth, 0) // Should remain unchanged
        XCTAssertEqual(coordinator.tabSelection, 0)
    }
    
    // MARK: - State Restoration Tests
    
    func testEncodeNavigationState() {
        coordinator.navigate(to: .staging)
        coordinator.navigate(to: .profile)
        coordinator.switchToTab(2)
        
        let data = coordinator.encodeNavigationState()
        
        XCTAssertNotNil(data)
        XCTAssertGreaterThan(data?.count ?? 0, 0)
    }
    
    func testRestoreNavigationState() {
        coordinator.navigate(to: .staging)
        coordinator.switchToTab(2)
        
        guard let data = coordinator.encodeNavigationState() else {
            XCTFail("Failed to encode navigation state")
            return
        }
        
        // Create new coordinator and restore state
        let newCoordinator = NavigationCoordinator()
        newCoordinator.restoreNavigationState(from: data)
        
        XCTAssertEqual(newCoordinator.tabSelection, 2)
        // Note: NavigationPath restoration is limited, so we only test tab selection
    }
    
    func testRestoreNavigationStateWithInvalidData() {
        let invalidData = Data("invalid".utf8)
        
        coordinator.restoreNavigationState(from: invalidData)
        
        // Should not crash and maintain default state
        XCTAssertEqual(coordinator.navigationDepth, 0)
        XCTAssertEqual(coordinator.tabSelection, 0)
    }
    
    // MARK: - Performance Tests
    
    func testNavigationPerformance() {
        measure {
            for i in 0..<100 {
                if i % 2 == 0 {
                    coordinator.navigate(to: .staging)
                } else {
                    coordinator.navigate(to: .profile)
                }
            }
            coordinator.popToRoot()
        }
    }
    
    func testLargeNavigationStack() {
        // Test with a large navigation stack
        for i in 0..<1000 {
            if i % 3 == 0 {
                coordinator.navigate(to: .staging)
            } else if i % 3 == 1 {
                coordinator.navigate(to: .profile)
            } else {
                coordinator.navigate(to: .recipeGenerator)
            }
        }
        
        XCTAssertEqual(coordinator.navigationDepth, 1000)
        XCTAssertFalse(coordinator.isAtRoot)
        XCTAssertTrue(coordinator.canNavigateBack)
        
        // Test performance of clearing large stack
        measure {
            coordinator.popToRoot()
        }
        
        XCTAssertEqual(coordinator.navigationDepth, 0)
        XCTAssertTrue(coordinator.isAtRoot)
    }
    
    // MARK: - Memory Management Tests
    
    func testMemoryManagement() {
        weak var weakCoordinator: NavigationCoordinator?
        
        autoreleasepool {
            let localCoordinator = NavigationCoordinator()
            weakCoordinator = localCoordinator
            
            // Perform some operations
            localCoordinator.navigate(to: .staging)
            localCoordinator.navigate(to: .profile)
            localCoordinator.switchToTab(1)
        }
        
        // Coordinator should be deallocated
        XCTAssertNil(weakCoordinator)
    }
    
    // MARK: - Thread Safety Tests
    
    func testMainActorIsolation() async {
        // Verify that all operations are properly isolated to MainActor
        await MainActor.run {
            coordinator.navigate(to: .staging)
            coordinator.switchToTab(1)
            coordinator.popBack()
            
            XCTAssertEqual(coordinator.navigationDepth, 0)
            XCTAssertEqual(coordinator.tabSelection, 1)
        }
    }
    
    // MARK: - Edge Cases
    
    func testNavigationWithComplexRoutes() {
        let images = Array(repeating: UIImage(), count: 10)
        let visionResponses = Array(repeating: "test response", count: 10)
        let ingredients = Array(repeating: Ingredient(id: UUID(), name: "Test", category: .produce, confidence: 0.9), count: 20)
        
        coordinator.navigateToBatchProcessing(images: images)
        coordinator.navigateToBatchVisionResults(images: images, visionResponses: visionResponses)
        coordinator.navigateToResults(ingredients: ingredients)
        
        XCTAssertEqual(coordinator.navigationDepth, 3)
        XCTAssertTrue(coordinator.canNavigateBack)
    }
    
    func testNavigationStateConsistency() {
        // Test that navigation state remains consistent across operations
        coordinator.navigate(to: .staging)
        let depth1 = coordinator.navigationDepth
        let canNavigate1 = coordinator.canNavigateBack
        let isRoot1 = coordinator.isAtRoot
        
        coordinator.navigate(to: .profile)
        let depth2 = coordinator.navigationDepth
        let canNavigate2 = coordinator.canNavigateBack
        let isRoot2 = coordinator.isAtRoot
        
        coordinator.popBack()
        let depth3 = coordinator.navigationDepth
        let canNavigate3 = coordinator.canNavigateBack
        let isRoot3 = coordinator.isAtRoot
        
        // Verify consistency
        XCTAssertEqual(depth1, 1)
        XCTAssertTrue(canNavigate1)
        XCTAssertFalse(isRoot1)
        
        XCTAssertEqual(depth2, 2)
        XCTAssertTrue(canNavigate2)
        XCTAssertFalse(isRoot2)
        
        XCTAssertEqual(depth3, 1)
        XCTAssertTrue(canNavigate3)
        XCTAssertFalse(isRoot3)
        
        // Should match initial state after first navigation
        XCTAssertEqual(depth1, depth3)
        XCTAssertEqual(canNavigate1, canNavigate3)
        XCTAssertEqual(isRoot1, isRoot3)
    }
} 