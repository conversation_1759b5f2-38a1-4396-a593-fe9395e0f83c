import XCTest
import SwiftUI
import Vision
import AVFoundation
@testable import IngredientScanner

class EnhancedCameraIntegrationTests: XCTestCase {
    
    var cameraService: EnhancedCameraService!
    
    override func setUp() {
        super.setUp()
        cameraService = EnhancedCameraService()
    }
    
    override func tearDown() {
        cameraService = nil
        super.tearDown()
    }
    
    // MARK: - Camera Permission Tests
    
    func testCameraPermissionHandling() async {
        // Test that camera permission is properly handled
        let hasPermission = await cameraService.requestCameraPermission()
        
        // On simulator, this should handle gracefully
        XCTAssertTrue(hasPermission || !hasPermission, "Permission check should complete without crashing")
    }
    
    // MARK: - Vision Processing Tests
    
    func testEnhancedVisionProcessing() async throws {
        // Create a test image with text
        let testImage = createTestImageWithText("Test Ingredients: Flour, Sugar, Salt")
        
        do {
            let result = try await cameraService.processImageWithEnhancedVision(testImage)
            
            // Verify result structure
            XCTAssertNotNil(result.localVisionText)
            XCTAssertNotNil(result.googleVisionResult)
            XCTAssertGreaterThanOrEqual(result.confidence, 0.0)
            XCTAssertLessThanOrEqual(result.confidence, 1.0)
            XCTAssertGreaterThanOrEqual(result.contourCount, 0)
            
            // Verify combined text is not empty
            XCTAssertFalse(result.combinedText.isEmpty, "Combined text should not be empty")
            
        } catch {
            // If Google Vision API is not configured, this is expected
            if case APIError.apiKeyNotConfigured = error {
                XCTAssert(true, "Expected error when API key is not configured")
            } else {
                XCTFail("Unexpected error: \(error)")
            }
        }
    }
    
    // MARK: - Ultra Wide Camera Tests
    
    func testUltraWideCameraAvailability() {
        let discoverySession = AVCaptureDevice.DiscoverySession(
            deviceTypes: [.builtInUltraWideCamera],
            mediaType: .video,
            position: .back
        )
        
        // This test verifies the camera detection logic works
        let hasUltraWide = !discoverySession.devices.isEmpty
        XCTAssertTrue(hasUltraWide || !hasUltraWide, "Ultra wide detection should complete without crashing")
    }
    
    // MARK: - Error Handling Tests
    
    func testInvalidImageHandling() async {
        // Test with an invalid image
        let invalidImage = UIImage() // Empty image
        
        do {
            _ = try await cameraService.processImageWithEnhancedVision(invalidImage)
            XCTFail("Should have thrown an error for invalid image")
        } catch CameraError.invalidImage {
            XCTAssert(true, "Correctly handled invalid image")
        } catch {
            XCTFail("Unexpected error type: \(error)")
        }
    }
    
    // MARK: - Integration Tests
    
    func testServiceContainerIntegration() {
        let serviceContainer = ServiceContainer.shared
        
        // Verify enhanced camera service is properly integrated
        XCTAssertNotNil(serviceContainer.enhancedCameraService)
        
        // Verify it's the same type as our service
        XCTAssertTrue(type(of: serviceContainer.enhancedCameraService) == EnhancedCameraService.self)
    }
    
    func testStagingViewModelIntegration() {
        let coordinator = NavigationCoordinator()
        let viewModel = StagingViewModel(navigationCoordinator: coordinator)
        
        // Verify enhanced camera property exists
        XCTAssertFalse(viewModel.showingEnhancedCamera, "Enhanced camera should start as false")
        
        // Test state changes
        viewModel.showingEnhancedCamera = true
        XCTAssertTrue(viewModel.showingEnhancedCamera, "Enhanced camera state should update")
    }
    
    // MARK: - Helper Methods
    
    private func createTestImageWithText(_ text: String) -> UIImage {
        let size = CGSize(width: 300, height: 100)
        let renderer = UIGraphicsImageRenderer(size: size)
        
        return renderer.image { context in
            // White background
            UIColor.white.setFill()
            context.fill(CGRect(origin: .zero, size: size))
            
            // Black text
            UIColor.black.setFill()
            let attributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 16),
                .foregroundColor: UIColor.black
            ]
            
            let textRect = CGRect(x: 10, y: 30, width: size.width - 20, height: 40)
            text.draw(in: textRect, withAttributes: attributes)
        }
    }
}

// MARK: - Mock Navigation Coordinator for Testing
extension NavigationCoordinator {
    convenience init() {
        self.init()
    }
} 