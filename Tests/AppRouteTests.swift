import XCTest
import SwiftUI
import UIKit
@testable import IngredientScanner

/// Comprehensive unit tests for AppRoute enum
/// Validates Hashable conformance, equality comparison, and all navigation cases
class AppRouteTests: XCTestCase {
    
    // MARK: - Test Data
    
    private var mockImages: [UIImage] {
        [
            UIImage(systemName: "photo")!,
            UIImage(systemName: "camera")!,
            UIImage(systemName: "square.and.arrow.up")!
        ]
    }
    
    private var mockVisionResponses: [String] {
        [
            "Vision response 1 with some detected text content",
            "Vision response 2 with different detected text content"
        ]
    }
    
    private var mockIngredients: [Ingredient] {
        [
            Ingredient(name: "Tomato", category: .produce),
            Ingredient(name: "Cheese", category: .dairy),
            Ingredient(name: "Bread", category: .grainsPastaLegumes)
        ]
    }
    
    private var mockRecipe: Recipe {
        Recipe(
            recipeTitle: "Test Recipe",
            description: "A test recipe",
            ingredients: ["Tomato", "Cheese"],
            instructions: ["Step 1", "Step 2"],
            nutrition: Recipe.NutritionInfo(
                calories: "200",
                protein: "10g",
                carbs: "20g",
                fat: "5g"
            )
        )
    }
    
    // MARK: - Hashable Tests
    
    func testSimpleRoutesHashable() {
        let routes: [AppRoute] = [
            .staging,
            .profile,
            .signIn,
            .preferencesEdit,
            .recipeGenerator
        ]

        for route in routes {
            let hash1 = route.hashValue
            let hash2 = route.hashValue
            XCTAssertEqual(hash1, hash2, "Hash should be consistent for \(route)")
        }
    }
    
    func testBatchProcessingHashable() {
        let route1 = AppRoute.batchProcessing(images: mockImages)
        let route2 = AppRoute.batchProcessing(images: mockImages)
        let route3 = AppRoute.batchProcessing(images: Array(mockImages.prefix(2)))
        
        // Same images should have same hash
        XCTAssertEqual(route1.hashValue, route2.hashValue)
        
        // Different images should have different hash
        XCTAssertNotEqual(route1.hashValue, route3.hashValue)
    }
    
    func testBatchVisionResultsHashable() {
        let route1 = AppRoute.batchVisionResults(images: mockImages, visionResponses: mockVisionResponses)
        let route2 = AppRoute.batchVisionResults(images: mockImages, visionResponses: mockVisionResponses)
        let route3 = AppRoute.batchVisionResults(images: mockImages, visionResponses: ["Different response"])
        
        XCTAssertEqual(route1.hashValue, route2.hashValue)
        XCTAssertNotEqual(route1.hashValue, route3.hashValue)
    }
    
    func testResultsHashable() {
        let route1 = AppRoute.results(ingredients: mockIngredients)
        let route2 = AppRoute.results(ingredients: mockIngredients)
        let route3 = AppRoute.results(ingredients: Array(mockIngredients.prefix(2)))
        
        XCTAssertEqual(route1.hashValue, route2.hashValue)
        XCTAssertNotEqual(route1.hashValue, route3.hashValue)
    }
    
    func testRecipeDetailHashable() {
        let route1 = AppRoute.recipeDetail(recipe: mockRecipe)
        let route2 = AppRoute.recipeDetail(recipe: mockRecipe)
        
        // Same recipe ID should have same hash
        XCTAssertEqual(route1.hashValue, route2.hashValue)
    }
    
    #if DEBUG
    func testDebugRouteHashable() {
        let route1 = AppRoute.debug(
            visionResponse: "Vision response",
            geminiResponse: "Gemini response",
            ingredients: mockIngredients
        )
        let route2 = AppRoute.debug(
            visionResponse: "Vision response",
            geminiResponse: "Gemini response",
            ingredients: mockIngredients
        )
        
        XCTAssertEqual(route1.hashValue, route2.hashValue)
    }
    #endif
    
    // MARK: - Equatable Tests
    
    func testSimpleRoutesEquality() {
        XCTAssertEqual(AppRoute.staging, AppRoute.staging)
        XCTAssertEqual(AppRoute.profile, AppRoute.profile)

        XCTAssertNotEqual(AppRoute.staging, AppRoute.profile)
        XCTAssertNotEqual(AppRoute.signIn, AppRoute.preferencesEdit)
    }
    
    func testBatchProcessingEquality() {
        let route1 = AppRoute.batchProcessing(images: mockImages)
        let route2 = AppRoute.batchProcessing(images: mockImages)
        let route3 = AppRoute.batchProcessing(images: Array(mockImages.prefix(2)))
        
        XCTAssertEqual(route1, route2)
        XCTAssertNotEqual(route1, route3)
    }
    
    func testBatchVisionResultsEquality() {
        let route1 = AppRoute.batchVisionResults(images: mockImages, visionResponses: mockVisionResponses)
        let route2 = AppRoute.batchVisionResults(images: mockImages, visionResponses: mockVisionResponses)
        let route3 = AppRoute.batchVisionResults(images: mockImages, visionResponses: ["Different"])
        
        XCTAssertEqual(route1, route2)
        XCTAssertNotEqual(route1, route3)
    }
    
    func testResultsEquality() {
        let route1 = AppRoute.results(ingredients: mockIngredients)
        let route2 = AppRoute.results(ingredients: mockIngredients)
        let route3 = AppRoute.results(ingredients: Array(mockIngredients.prefix(2)))
        
        XCTAssertEqual(route1, route2)
        XCTAssertNotEqual(route1, route3)
    }
    
    func testRecipeDetailEquality() {
        let route1 = AppRoute.recipeDetail(recipe: mockRecipe)
        let route2 = AppRoute.recipeDetail(recipe: mockRecipe)
        
        // Same recipe ID should be equal
        XCTAssertEqual(route1, route2)
    }
    
    // MARK: - Edge Cases Tests
    
    func testEmptyArraysHandling() {
        let emptyImagesRoute = AppRoute.batchProcessing(images: [])
        let emptyResponsesRoute = AppRoute.batchVisionResults(images: [], visionResponses: [])
        let emptyIngredientsRoute = AppRoute.results(ingredients: [])
        
        // Should not crash with empty arrays
        XCTAssertNotNil(emptyImagesRoute.hashValue)
        XCTAssertNotNil(emptyResponsesRoute.hashValue)
        XCTAssertNotNil(emptyIngredientsRoute.hashValue)
    }
    
    func testLargeDataHandling() {
        // Test with large arrays to ensure performance is acceptable
        let largeImages = Array(repeating: UIImage(systemName: "photo")!, count: 100)
        let largeResponses = Array(repeating: "Large response content", count: 100)
        let largeIngredients = Array(repeating: Ingredient(name: "Test", category: .other), count: 100)
        
        let route1 = AppRoute.batchProcessing(images: largeImages)
        let route2 = AppRoute.batchVisionResults(images: largeImages, visionResponses: largeResponses)
        let route3 = AppRoute.results(ingredients: largeIngredients)
        
        // Should handle large datasets efficiently
        let startTime = CFAbsoluteTimeGetCurrent()
        _ = route1.hashValue
        _ = route2.hashValue
        _ = route3.hashValue
        let timeElapsed = CFAbsoluteTimeGetCurrent() - startTime
        
        XCTAssertLessThan(timeElapsed, 0.1, "Hashing should be fast even with large datasets")
    }
    
    func testStringPrefixHandling() {
        let longResponse = String(repeating: "A", count: 1000)
        let route = AppRoute.batchVisionResults(images: mockImages, visionResponses: [longResponse])
        
        // Should efficiently handle long strings using prefix
        XCTAssertNotNil(route.hashValue)
    }
    
    // MARK: - NavigationStack Compatibility Tests
    
    func testNavigationStackCompatibility() {
        // Test that AppRoute can be used with NavigationPath
        var navigationPath = NavigationPath()
        
        let routes: [AppRoute] = [
            .staging,
            .batchProcessing(images: mockImages),
            .results(ingredients: mockIngredients),
            .recipeDetail(recipe: mockRecipe)
        ]
        
        for route in routes {
            navigationPath.append(route)
        }
        
        XCTAssertEqual(navigationPath.count, routes.count)
    }
    
    // MARK: - Memory Management Tests
    
    func testMemoryEfficiency() {
        // Test that routes with large associated values don't cause memory issues
        let largeImages = Array(repeating: UIImage(systemName: "photo")!, count: 50)
        let routes = Array(0..<100).map { _ in
            AppRoute.batchProcessing(images: largeImages)
        }
        
        // Should be able to create many routes without memory issues
        XCTAssertEqual(routes.count, 100)
        
        // Test hash consistency
        let firstHash = routes[0].hashValue
        let lastHash = routes[99].hashValue
        XCTAssertEqual(firstHash, lastHash, "Same routes should have same hash")
    }
}

// MARK: - Performance Tests

extension AppRouteTests {
    
    func testHashingPerformance() {
        let routes: [AppRoute] = [
            .batchProcessing(images: mockImages),
            .batchVisionResults(images: mockImages, visionResponses: mockVisionResponses),
            .results(ingredients: mockIngredients),
            .recipeDetail(recipe: mockRecipe)
        ]
        
        measure {
            for _ in 0..<1000 {
                for route in routes {
                    _ = route.hashValue
                }
            }
        }
    }
    
    func testEqualityPerformance() {
        let route1 = AppRoute.batchProcessing(images: mockImages)
        let route2 = AppRoute.batchProcessing(images: mockImages)
        
        measure {
            for _ in 0..<10000 {
                _ = route1 == route2
            }
        }
    }
} 