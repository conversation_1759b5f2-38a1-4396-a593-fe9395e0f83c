# IngredientScanner

A complete iOS application that uses computer vision and AI to scan images and extract food ingredients from receipts, ingredient lists, or refrigerator contents.

## Features

### Core Functionality
- **Image Capture**: Take photos or select from photo library
- **AI-Powered Detection**: Uses Google Vision API for text detection and Gemini API for ingredient extraction
- **Interactive Results**: Review, check/uncheck, and add ingredients to the final list
- **Pantry Management**: Organize ingredients by categories
- **Recipe Generation**: AI-powered meal suggestions based on available ingredients
- **Shopping List**: Smart shopping list with ingredient tracking

### User Management (NEW)
- **Multi-Platform Authentication**: Apple Sign-In, Google Sign-In, and Email authentication
- **Food Preferences**: Strict exclusions, dietary restrictions, and allergy management
- **Family Settings**: Configure household size and special dietary needs
- **Personalized Experience**: Customizable notifications and app preferences
- **Data Synchronization**: Seamless sync across devices with Firebase

### Technical Features
- **Debug Mode**: View raw API responses in development builds
- **MVVM-C Architecture**: Clean, scalable architecture with coordinators
- **Modular Design**: Independent feature modules for easy maintenance and testing

## Requirements

- iOS 17.0+
- Xcode 14.0+
- Swift 6.0+
- Google Cloud Vision API key
- Google Gemini API key
- Firebase project with Authentication and Firestore enabled

## Setup Instructions

### 1. API Keys Configuration

Before running the app, you need to add your API keys:

1. Navigate to `IngredientScanner/Utilities/APIKeys.swift`
2. Replace the placeholder values with your actual API keys:

```swift
enum APIKeys {
    static let googleVisionAPIKey = "YOUR_ACTUAL_GOOGLE_VISION_API_KEY"
    static let geminiAPIKey = "YOUR_ACTUAL_GEMINI_API_KEY"
}
```

**Important**: The `APIKeys.swift` file is gitignored to protect your API keys. Never commit real API keys to version control.

### 2. Getting API Keys

#### Google Vision API:
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Cloud Vision API
4. Create credentials (API Key)
5. Copy the API key

#### Gemini API:
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create an API key
3. Copy the API key

### 3. Firebase Configuration

The app uses Firebase for user authentication and data storage:

1. **Firebase Project Setup**:
   - The project is already configured with Firebase project `gtest-1c610`
   - `GoogleService-Info.plist` is included and configured
   - Firebase Auth and Firestore dependencies are already added

2. **Authentication Providers**:
   - **Apple Sign-In**: Already configured with proper entitlements
   - **Google Sign-In**: URL schemes configured in Info.plist
   - **Email/Password**: Enable in Firebase Console → Authentication → Sign-in method

3. **Firestore Database**:
   - Create a Firestore database in your Firebase project
   - Set up security rules for user data protection

### 4. Generating the Xcode Project

Since the project is provided as source files, you need to generate the Xcode project first:

```bash
cd IngredientScanner
./generate_xcodeproj.sh
```

This script will:
- Install xcodegen if needed (requires Homebrew)
- Generate the Xcode project file
- Provide instructions for next steps

### 5. Running the Project

1. Open `IngredientScanner.xcodeproj` in Xcode
2. Select your target device (simulator or physical device)
3. Select a development team in the project settings (required for device testing)
4. Build and run (⌘+R)

## Architecture

The app follows the MVVM-C (Model-View-ViewModel-Coordinator) pattern:

- **Models**: Simple data structures (`Ingredient`)
- **Views**: SwiftUI views for UI
- **ViewModels**: Business logic and state management
- **Coordinator**: Navigation flow management
- **Services**: Actor-based API services for thread-safe network operations

## Project Structure

```
IngredientScanner/
├── Application/          # App entry point and assets
├── Coordinator/          # Navigation coordinator with tab management
├── Services/            # API services (Actors) and ServiceContainer
├── Models/              # Data models (Ingredient, Recipe, etc.)
├── Features/            # Feature modules
│   ├── 1_ImageCapture/  # Camera and image capture
│   ├── 2_ImagePreview/  # Batch image processing
│   ├── 3_Results/       # Ingredient recognition results
│   ├── Debug/           # Debug view (DEBUG builds only)
│   ├── Pantry/          # Pantry management with categories
│   ├── RecipeGenerator/ # AI-powered recipe generation
│   └── Profile/         # User authentication and preferences
└── Utilities/           # Helper files and utilities
```

## Authentication & User Management

The app includes Firebase-based authentication and user preference management:

### Key Features
- **Firebase Authentication**: Support for Apple, Google, and Email sign-in
- **Anonymous Usage**: Full app functionality without sign-in
- **Cloud Sync**: User preferences sync across devices when authenticated
- **Local Storage**: Preferences stored locally with UserDefaults
- **Family Support**: Configure household size and dietary preferences

### Authentication Flow
- Users can use the app anonymously with full functionality
- Sign-in options available for preference syncing across devices
- Authentication handled by `AuthenticationService.swift`
- UI components in `Features/Profile/` directory

## Privacy & Permissions

The app follows Apple's privacy-first design:
- **Camera**: Handled automatically by UIImagePickerController when taking photos
- **Photo Library**: No permissions needed - PHPickerViewController provides privacy-first photo selection

No explicit permission requests are made. The system handles privacy automatically when you use camera or photo selection features.

## Debug Mode

In DEBUG builds, the app includes a debug view that shows:
- Raw Google Vision API response
- Processed Gemini API response

This helps in understanding how the APIs are processing your images.

## Notes

- The app is designed to work immediately after adding valid API keys
- All network operations are handled asynchronously with proper error handling
- The UI is built entirely with SwiftUI for modern iOS development
- Images are compressed before sending to APIs to optimize performance

## Recent Updates

### User Management Module (Latest)
- ✅ **Complete modular architecture** - Independent user management system
- ✅ **Firebase Authentication** - Apple, Google, and Email sign-in support
- ✅ **User Preferences** - Food restrictions, allergies, and dietary settings
- ✅ **Family Configuration** - Multi-user household support
- ✅ **Data Synchronization** - Cross-device user data sync
- ✅ **Personalization** - Custom notifications and app preferences

## Troubleshooting

### General Issues
1. **"No ingredients found" error**: Ensure the image clearly shows text containing food items
2. **API errors**: Verify your API keys are correct and have the necessary permissions
3. **Camera not working**: Check that camera permissions are granted in Settings

### User Management Issues
4. **Login failures**: Check Firebase configuration and ensure authentication providers are enabled
5. **Data not syncing**: Verify Firestore is properly configured and user has internet connection
6. **Preferences not saving**: Check user authentication status and Firebase permissions

## Development Status

### Current Phase: User Management Module Development
- [x] Module architecture created
- [x] Firebase configuration verified
- [ ] Core authentication implementation
- [ ] User preferences system
- [ ] Profile management interface
- [ ] Integration with existing features

### Next Steps
1. Implement authentication services (Apple, Google, Email)
2. Create user preference models and services
3. Build user interface components
4. Integrate with recipe generation and pantry systems
5. Add comprehensive testing

## License

This is a demo project for educational purposes.

---

## ✅ **Development History**

### **Latest: User Management Module** 🆕
- ✅ **Firebase Authentication**: Integrated Apple, Google, and Email sign-in
- ✅ **Firebase Integration**: Verified and configured authentication infrastructure
- ✅ **Comprehensive Planning**: Detailed module structure and integration strategy
- ✅ **Documentation**: Complete module documentation and integration guides

### **Previous Updates**
- ✅ **Modular ingredient scanner** - Reusable scanning module
- ✅ **Swift 6.0 upgrade** - Modern concurrency and type safety
- ✅ **Recipe generation** - AI-powered meal suggestions
- ✅ **Pantry management** - Organized ingredient storage
- ✅ **Shopping list** - Smart shopping assistance


