### PRD: Single-Screen Scan + Pantry Organizer (iOS 17+)

## Overview
Deliver a simplified scanning experience and a one-tap pantry cleanup. Replace the multi-screen scanning flow with a single-screen “Scan” tab that runs Vision + Gemini and navigates directly to Results. Introduce “Organize Pantry” to clean all saved items in-place with minimal user input.

## Goals
- Single-screen Scan with inline preview and one “Processing…” overlay.
- Vision runs OCR + Labels across up to 10 images; Gemini canonicalizes into strict pantry categories.
- No local name normalization or blacklist; duplicates allowed on save.
- One-tap “Organize Pantry” cleans and optionally merges duplicates; changes apply automatically with a brief summary.

## Non-Goals
- Changes to sign-in/sync, recipes, or profile flows.
- Subcategory taxonomies or nutrition data changes.
- Server-side persistence beyond current SwiftData.

## Platforms
- iOS 17+ only.

## Personas & Use Cases
- Busy home cook scanning receipts/packaging to quickly seed pantry.
- Users maintaining pantry over time; occasional one-tap cleanup to fix categories/names/duplicates.

## User Experience

### Scan Tab (single screen)
- Two primary buttons:
  - “Scan Receipts and Ingredients” (camera)
  - “Choose from Library” (gallery)
- Inline image grid (0–10 images) with per-image delete (X).
- Process button appears when ≥1 image.
- On Process: single full-screen overlay “Processing…” (no cancel). Run Vision (OCR + Labels) on all images, then Gemini to canonicalize. Navigate directly to Results.

### Results
- List of cleaned ingredients grouped by category with checkboxes and inline pencil edit per item.
- Edit = rename and recategorize only (no add/delete here).
- Actions:
  - “Add Selected to Pantry” → save, switch to Pantry tab, reset Scan.
  - “Restart Scanning” → clear images, return to empty Scan tab.

### Pantry
- Existing browsing/manual add remains (via IngredientLibrary suggestions or free-form).
- New action: “Organize Pantry”
  - Single tap → spinner “Analyzing pantry…”
  - Auto-applies cleanup (fix categories, strip brand/qty/fluff, remove non-food/gibberish, optionally merge duplicates).
  - Optional summary UI: “Cleaned 32; merged 3; removed 1 non-food.”

## Functional Requirements

- Scan
  - Select up to 10 images from camera/gallery; show preview grid; per-image delete.
  - Batch Vision (OCR + Labels) against all images with concurrency cap (3–4); optional client-side resize if longest side > 2000px.
  - Send all VisionOutput to Gemini in one batch call; enforce category ∈ PantryCategory.rawValue; names pass-through (descriptors ok); exclude non-food/gibberish.
  - Navigate to Results automatically.

- Organize Pantry
  - Process full pantry in batches (100–200 items per request depending on token limits).
  - Gemini outputs a CleanUpPlan with updates/removals/merges.
  - Apply all changes automatically; show brief summary.

## Architecture Changes

- Navigation & Views
  - Keep: AppRoute.staging, AppRoute.results.
  - Remove: AppRoute.batchProcessing, AppRoute.batchVisionResults, AppRoute.batchGeminiProcessing and associated views/VMs in Features/2_ImagePreview/*.
  - StagingView/StagingViewModel:
    - Update max images to 10; integrate camera/gallery buttons and grid.
    - Replace navigation to intermediate screens with inline pipeline + overlay “Processing…”.
  - ResultsView/ResultsViewModel:
    - Keep checkbox selection and inline edit.
    - Add “Restart Scanning” to reset the scan tab (clears staged images and returns to empty Scan).

- Services
  - GoogleVisionAPIService
    - Add batchAnalyze(images: [UIImage]) -> [VisionOutput].
    - Concurrency cap ~3–4; optional resize > 2000px longest side.
  - GeminiAPIService (scanning cleanup)
    - Add canonicalizeIngredients(visionOutputs: [VisionOutput], allowedCategories: [String]) -> [Ingredient].
    - No local normalization or blacklist; strict category membership; pure JSON output.
  - PantryService
    - Remove IngredientNameNormalizer usage on save and load; allow duplicates on save.
  - IngredientLibrary
    - No involvement in scan pipeline; continues to power manual add.
  - PantryOrganizerService (new)
    - Input: entire pantry items [{id, name, category}] (chunked).
    - Output: CleanUpPlan (updates/removals/merges); auto-apply with summary.

## API Contracts

```swift
struct VisionOutput: Sendable {
    var ocrText: String
    var labels: [(text: String, confidence: Float)]
}

struct CleanUpPlan: Sendable {
    var updates: [UpdateOp]
    var removals: [UUID]
    var merges: [MergeOp]
}

struct UpdateOp: Sendable {
    var id: UUID
    var newName: String
    var newCategory: PantryCategory
}

struct MergeOp: Sendable {
    var winnerId: UUID
    var loserIds: [UUID]
}
```

```swift
actor GoogleVisionAPIService {
    func batchAnalyze(images: [UIImage]) async throws -> [VisionOutput]
}
```

```swift
actor GeminiAPIService {
    func canonicalizeIngredients(
        visionOutputs: [VisionOutput],
        allowedCategories: [String]
    ) async throws -> [Ingredient]
}
```

```swift
actor PantryOrganizerService {
    func organize(items: [Ingredient], allowedCategories: [String]) async throws -> CleanUpPlan
    func apply(plan: CleanUpPlan) async throws -> PantryOrganizerSummary
}

struct PantryOrganizerSummary: Sendable {
    var updatedCount: Int
    var removedCount: Int
    var mergedCount: Int
}
```

## Allowed Categories (from PantryCategory.rawValue)
- Baking & Sweeteners
- Oils, Vinegars & Condiments
- Spices & Seasonings
- Proteins
- Produce
- Other
- Dairy
- Plant-based Alternatives
- Grains, Pasta & Legumes
- Nuts & Seeds
- Canned & Broths
- Bakery
- Snacks

## Prompt Specifications

- Scan cleanup prompt
  - Inputs per image: OCR text and top labels; AllowedCategories list (exact strings above).
  - Instructions:
    - Output JSON array only: [{"name":"...","category":"..."}]
    - Category must be exactly one of AllowedCategories
    - Remove brand/size/quantity/marketing fluff; preserve meaningful descriptors (e.g., “whole milk”)
    - Exclude non-food/gibberish entirely
  - No local normalization or blacklist.

- Organize Pantry prompt
  - Inputs: full pantry [{id, name, category}] + AllowedCategories.
  - Instructions:
    - For each item, return cleaned {id, name, category} with exact category match
    - For non-food/gibberish: {id, remove: true}
    - For duplicates: propose merges {winnerId, loserIds}
    - Output JSON only
  - Chunk inputs to fit token limits.

## Error Handling

- Scan
  - If some images fail Vision: continue with others; note in Results header.
  - If Gemini fails: show error and return to Scan with images preserved.

- Organize Pantry
  - Apply plan in batches; all-or-nothing per batch to maintain consistency.
  - On batch failure: skip batch, show partial success summary, “Re-run” option.

## Performance & Cost
- Scan: ≤10 images; Vision concurrency 3–4; single Gemini batch call per scan.
- Organize Pantry: batch pantry into ~100–200 items per call; auto-apply with brief summary.

## Security & Privacy
- Images and extracted text processed via Google Vision/Gemini according to their policies.
- No PII stored beyond what the app already persists; SwiftData as current.

## Accessibility
- Buttons and progress indications labeled; overlays are screen-reader friendly; high-contrast labels.

## Telemetry (optional)
- Track counts: images processed, items cleaned/merged/removed, organizer runs, failures.

## Testing Plan

- Unit
  - Canonicalization parser: accepts only category ∈ allowedCategories, names pass-through.
  - PantryOrganizerService: apply CleanUpPlan correctly (update/delete/merge).

- UI
  - Staging: 10-image cap; grid add/remove; overlay spinner; direct Results.
  - Pantry: “Organize Pantry” auto-applies; summary visible.

- Integration (with mocks)
  - Vision outputs → Gemini outputs → Results → PantryService save (duplicates allowed).
  - Full pantry → Organizer prompt → plan → applied to SwiftData.

## Milestones / PR Sequence

1) Navigation cleanup: keep staging/results, stub pipeline; remove Features/2_ImagePreview/* and batch routes.
2) Staging upgrades: 10-image cap, two buttons, inline grid, Process overlay, placeholder pipeline → Results.
3) Vision batch API: batchAnalyze with OCR+Labels; concurrency cap; resize; tests.
4) Gemini canonicalization: canonicalizeIngredients with strict categories; tests.
5) Wire pipeline: StagingViewModel.process = Vision → Gemini → navigate to Results; overlay lifecycle.
6) PantryService: remove normalization/dedupe; allow duplicates; update Add-to-Pantry flow.
7) Organize Pantry: new service, toolbar button, automatic apply + summary; tests.
8) Polish, UI tests, copy updates, edge cases.

## Acceptance Criteria

- Scan tab supports 0–10 images with delete/add-more; Process shows single overlay and navigates to Results.
- Vision runs OCR + Labels across all images with concurrency cap; optional resize > 2000px.
- Results shows cleaned items; edit renames/recategorizes; “Add Selected to Pantry” saves (duplicates allowed) and switches to Pantry; “Restart Scanning” resets scan.
- “Organize Pantry” applies cleanups automatically and shows summary (counts of updates/merges/removals).
- Gemini outputs adhere to pure JSON; categories exactly match PantryCategory.rawValue.
- App builds on iOS 17+ with existing sign-in/sync unchanged.

## Risks & Mitigations
- Prompt drift → Keep strict JSON and hard category list; robust parser drop non-compliant items.
- Token limits on organizer → Chunk inputs; summarize per-batch results.
- User surprise on auto-apply → Provide clear summary and allow re-run; changes are text-only.


