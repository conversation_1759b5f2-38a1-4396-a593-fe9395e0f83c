<context>
# Overview  
本项目旨在为IngredientScanner iOS应用构建一个健壮、可扩展的用户偏好设置云端同步系统。该系统将用户的所有个性化设置（如过敏原、饮食禁忌、家庭规模等）作为完整对象同步至Firebase Firestore，实现跨设备无缝漫游，并为未来付费订阅功能奠定基础。

# Core Features  
## 1. 用户偏好云端同步
- **功能描述**: 将UserPreferences对象完整上传并同步至Firestore
- **重要性**: 提升用户体验，实现跨设备数据漫游，避免重复配置
- **实现方式**: 使用Firebase Firestore的Codable数据映射，严格遵循官方最佳实践

## 2. 自动用户配置初始化  
- **功能描述**: 新用户首次登录时自动创建默认偏好设置
- **重要性**: 确保所有用户都有完整的配置基础
- **实现方式**: 检测用户文档是否存在，不存在则创建默认UserPreferences

## 3. 实时偏好设置同步
- **功能描述**: 用户修改设置后立即同步到云端
- **重要性**: 保证数据一致性，支持多设备使用场景
- **实现方式**: 在设置修改事件中调用savePreferences方法

## 4. 离线数据持久化支持
- **功能描述**: 支持离线修改，网络恢复后自动同步
- **重要性**: 提供流畅的用户体验，无需考虑网络状态
- **实现方式**: 依赖Firebase Firestore的内置离线缓存机制。Firestore在iOS上默认开启离线持久化，无需额外配置复杂的离线逻辑

# User Experience  
## 用户画像
- **主要用户**: 注重健康饮食的家庭用户
- **使用场景**: 多设备使用（iPhone、iPad），需要家庭成员信息同步
- **核心需求**: 个性化饮食限制、过敏原管理、跨设备数据一致性

## 关键用户流程
1. **新用户注册流程**: 
   - 用户选择登录方式（Apple/Google/Email）
   - 系统自动创建默认偏好设置
   - 引导用户完善个人信息

2. **老用户登录流程**:
   - 用户登录成功
   - 系统自动获取云端偏好设置
   - 恢复个性化界面和设置

3. **设置修改流程**:
   - 用户在设置页面修改偏好
   - 点击保存按钮
   - 系统立即同步到云端
   - 提供操作反馈

## UI/UX考虑
- 加载状态指示器
- 同步状态反馈
- 错误处理用户提示
- 离线状态提示
</context>

<PRD>
# Technical Architecture  
## 系统组件
### 核心服务层
- **UserProfileService.swift**: 单例服务，负责所有Firestore用户数据交互
- **位置**: Services/目录
- **职责**: 创建、读取、更新用户偏好设置

### 数据模型
- **UserPreferences**: 现有模型，包含所有用户偏好设置
- **支持的数据类型**: 严格对应Firestore支持的数据类型
- **编码方式**: 使用Swift Codable协议

### Firebase集成
- **Firestore数据库**: 云端数据存储
- **Firebase Authentication**: 用户身份验证
- **数据结构**: users/{userId}文档 (UserPreferences对象将直接存储在该文档中，而非子集合)

## APIs和集成
### Firebase Firestore APIs
- **setData(from:)**: 使用Codable方式创建/更新文档
- **getDocument(as:)**: 使用Codable方式读取文档
- **离线持久化**: 默认启用，无需额外配置

### 集成点
1. **AuthenticationService集成**:
   - 登录成功后调用fetchOrCreateUserProfile
   - 处理新用户初始化和老用户数据恢复

2. **设置界面集成**:
   - PreferencesEditView等界面
   - 保存操作触发云端同步

## 基础设施要求
- Firebase项目配置
- Firestore数据库启用
- iOS 17.0+支持
- Swift 6.0兼容性

# Development Roadmap  
## Phase 1: 核心服务实现 (MVP)
### 1.1 UserProfileService创建
- 创建Services/UserProfileService.swift
- 实现单例模式
- 添加基础的保存和获取方法
- 集成Firebase Firestore依赖

### 1.2 数据模型适配
- 验证UserPreferences模型与Firestore兼容性
- 确保所有属性类型符合Firestore支持的数据类型
- 添加必要的Codable支持

### 1.3 基础安全配置
- 配置Firestore安全规则
- 确保用户只能访问自己的数据
- 测试安全规则有效性

## Phase 2: 认证服务集成
### 2.1 登录流程集成
- 在AuthenticationService中添加fetchOrCreateUserProfile方法
- 集成到Apple/Google/Email登录流程
- 处理新用户默认设置创建

### 2.2 用户状态管理
- 更新认证状态管理逻辑
- 添加用户偏好加载状态
- 实现错误处理机制

## Phase 3: UI集成和用户体验优化
### 3.1 设置界面集成
- 在PreferencesEditView中集成保存逻辑
- 添加同步状态指示器
- 实现操作反馈机制

### 3.2 加载和错误状态
- 添加数据加载指示器
- 实现网络错误处理
- 优化离线使用体验

## Phase 4: 测试和验证
### 4.1 功能测试
- 新用户注册测试
- 老用户登录测试
- 跨设备同步测试
- 离线操作测试

### 4.2 安全测试
- 验证安全规则有效性
- 测试数据访问权限
- 检查潜在安全漏洞

# Logical Dependency Chain
## 基础设施层 (Foundation)
1. **Firebase配置验证**: 确保现有Firebase项目配置正确
2. **Firestore安全规则**: 配置用户数据访问权限
3. **数据模型验证**: 确保UserPreferences与Firestore兼容

## 核心服务层 (Core Services)
4. **UserProfileService实现**: 创建核心数据服务
5. **错误处理机制**: 实现健壮的错误处理
6. **单元测试**: 验证服务功能正确性

## 集成层 (Integration)
7. **认证服务集成**: 在登录流程中集成用户偏好获取
8. **UI界面集成**: 在设置修改时集成数据保存
9. **状态管理**: 实现加载和同步状态管理

## 验证层 (Validation)
10. **端到端测试**: 完整用户流程测试
11. **跨设备测试**: 多设备同步验证
12. **性能优化**: 离线缓存和网络优化

# Risks and Mitigations  
## 技术挑战
### 风险1: Firebase配置复杂性
- **描述**: Firebase配置错误可能导致认证或数据同步失败
- **缓解措施**: 严格遵循官方文档，分步验证配置

### 风险2: 数据类型兼容性
- **描述**: Swift模型与Firestore数据类型不匹配
- **缓解措施**: 详细检查Firestore支持的数据类型，使用Codable最佳实践

### 风险3: 安全规则配置
- **描述**: 错误的安全规则可能导致数据泄露或访问被拒绝
- **缓解措施**: 使用提供的标准安全规则模板，进行充分测试

## MVP范围界定
### 当前阶段专注
- 用户偏好设置的基础同步功能
- 简单的错误处理和状态管理
- 核心用户流程支持

### 未来增强功能
- 高级同步冲突解决
- 批量数据操作优化
- 实时数据监听
- 离线队列管理

## 资源约束
### 开发时间
- 预计需要3-5个开发周期
- 重点关注MVP功能实现
- 预留充分测试时间

### API配额限制
- 了解Firestore使用限制和配额
- 设计时考虑读写操作频率
- 实现合理的缓存策略

# Appendix  
## 强制性参考资料
### Firebase官方文档
1.  **核心概念 (Core Concepts)**
    *   **官方文档首页:** [Cloud Firestore](https://firebase.google.com/docs/firestore)
        *   **指令:** 建立对 Firestore 的宏观理解。
    *   **数据模型:** [Data Model](https://firebase.google.com/docs/firestore/data-model)
        *   **指令:** **(关键)** 必须深刻理解 Firestore 的 `collection/document/subcollection` 结构。这是构建可扩展数据架构的基础。
    *   **支持的数据类型:** [Supported Data Types](https://firebase.google.com/docs/firestore/manage-data/data-types)
        *   **指令:** **(关键)** 确保 `UserPreferences` Swift 模型中使用的所有属性类型都与 Firestore 支持的类型完全对应。

2.  **基础操作 (Fundamental Operations)**
    *   **添加与管理数据:** [Add and Manage Data](https://firebase.google.com/docs/firestore/manage-data/add-data)
        *   **内容约束:** 必须使用 `setData(from: document)` 的 `Codable` 方式来创建或更新用户偏好文档。
    *   **读取数据:** [Get Data](https://firebase.google.com/docs/firestore/query-data/get-data)
        *   **内容约束:** 必须使用 `document.data(as: YourCodableType.self)` 的方式来解码文档。
    *   **查询数据:** [Query Data](https://firebase.google.com/docs/firestore/query-data/queries)
        *   **指令:** 了解如何构建简单和复合查询，为未来功能做准备。

3.  **Swift 最佳实践 (Swift Best Practices)**
    *   **Codable 数据映射:** [Model data with Codable](https://firebase.google.com/docs/firestore/solutions/swift-codable-data-mapping)
        *   **指令:** **(强制性)** 这是整个项目数据持久化的核心。严禁手动创建字典 `[String: Any]` 进行数据交换。

4.  **健壮性与安全 (Robustness & Security)**
    *   **离线数据持久化:** [Enable Offline Persistence](https://firebase.google.com/docs/firestore/manage-data/enable-offline)
        *   **指令:** 理解并信赖 iOS 上的默认离线缓存能力，无需编写复杂的网络状态判断逻辑。
    *   **错误处理:** [Handle Errors](https://firebase.google.com/docs/firestore/manage-data/transactions-and-batched-writes#handle_errors)
        *   **指令:** 所有数据库调用都必须包含在 `do-catch` 块中，并对错误进行日志记录。
    *   **安全规则:** [Security Rules](https://firebase.google.com/docs/firestore/security/get-started)
        *   **指令:** **(安全红线)** 必须阅读并理解此文档，以完全领会 `3.1` 章节中提供的安全规则的重要性。

5.  **限制与配额 (Limits & Quotas)**
    *   **使用限制:** [Usage and Limits](https://firebase.google.com/docs/firestore/quotas)
        *   **指令:** 了解关键限制（如文档大小、写入速率），确保设计不会触及天花板。
</PRD> 