{"master": {"tasks": [{"id": 28, "title": "Navigation Cleanup and Route Restructuring", "description": "Update navigation structure based on PRD requirements, with a focus on maintaining backward compatibility while enhancing the staging to results flow.", "status": "done", "dependencies": [], "priority": "high", "details": "1. Mark batch processing routes as deprecated rather than removing them immediately\n2. Update StagingView to support up to 10 images (increased from 3)\n3. Implement complete Vision → Gemini processing flow within StagingView\n4. Ensure Vision batch processing and Gemini normalization services are implemented first\n5. Add error handling for partial success scenarios\n\nCode changes:\n```swift\n// Update AppRoute enum to deprecate batch routes\nenum AppRoute {\n    case staging\n    case results\n    // Deprecated but maintained for backward compatibility\n    @available(*, deprecated, message: \"Use staging with direct processing instead\")\n    case batchProcessing\n    @available(*, deprecated, message: \"Use results instead\")\n    case batchVisionResults\n    @available(*, deprecated, message: \"Use results instead\")\n    case batchGeminiProcessing\n    // Keep other existing routes\n}\n\n// Update navigation coordinator to handle direct staging->results flow\nfunc navigateToResults(with ingredients: [Ingredient]) {\n    // Direct navigation from staging to results\n    currentRoute = .results(ingredients: ingredients)\n}\n\n// Add error handling for partial success\nfunc handleProcessingResults(results: [ProcessingResult]) {\n    let successfulIngredients = results.compactMap { result in\n        if case .success(let ingredient) = result {\n            return ingredient\n        }\n        return nil\n    }\n    \n    if !successfulIngredients.isEmpty {\n        navigateToResults(with: successfulIngredients)\n    } else {\n        // Handle complete failure case\n        showErrorAlert(message: \"Processing failed. Please try again.\")\n    }\n}\n```", "testStrategy": "1. Unit test AppRoute enum to ensure all routes exist with proper deprecation warnings\n2. Verify navigation flow from staging to results works correctly\n3. Test that deprecated routes still function but generate appropriate warnings\n4. Test that state is properly maintained during navigation\n5. Verify error handling for partial success scenarios\n6. Test StagingView with various image counts up to the new limit of 10", "subtasks": [{"id": 1, "title": "Update A<PERSON><PERSON><PERSON><PERSON> and <PERSON>s as Deprecated", "description": "Modify the AppRoute enum to mark batch processing routes as deprecated while keeping them functional for backward compatibility.", "status": "done", "dependencies": [], "details": "1. Open the file containing the AppRoute enum\n2. Keep the cases for staging and results\n3. Add @available(*, deprecated, message: \"...\") annotations to batchProcessing, batchVisionResults, and batchGeminiProcessing routes\n4. Update any associated route parameters to ensure they still work correctly\n5. Add documentation comments explaining the deprecation strategy\n6. Verify that the enum is properly updated and compiles without errors", "testStrategy": "1. Write unit tests to verify that all routes still exist in the AppRoute enum\n2. Test that route initialization works correctly for all routes\n3. Verify that deprecated routes generate appropriate compiler warnings\n4. Test backward compatibility with code that might still use deprecated routes"}, {"id": 2, "title": "Update Batch Processing Views for Compatibility", "description": "Update batch processing views to maintain backward compatibility while encouraging migration to the new direct processing flow.", "status": "done", "dependencies": [1], "details": "1. Identify all views and view models in Features/2_ImagePreview/* related to batch processing\n2. Add deprecation warnings to these files:\n   - BatchProcessingView.swift\n   - BatchProcessingViewModel.swift\n   - BatchVisionResultsView.swift\n   - BatchVisionResultsViewModel.swift\n   - BatchGeminiProcessingView.swift\n   - BatchGeminiProcessingViewModel.swift\n3. Add migration guidance in comments\n4. Ensure these views still function but redirect to the new flow when possible\n5. Update any resources to support both old and new flows during transition", "testStrategy": "1. Verify the project builds successfully with deprecation warnings\n2. Test that deprecated views still function correctly\n3. Verify redirection to new flow works as expected\n4. Test backward compatibility with existing code paths"}, {"id": 3, "title": "Update Navigation Coordinator for Direct Flow", "description": "Modify the navigation coordinator to handle direct navigation from staging to results while implementing the complete Vision → Gemini processing flow.", "status": "done", "dependencies": [1, 2], "details": "1. Update the navigation coordinator to implement direct staging->results flow\n2. Modify the navigateToResults method to accept ingredients parameter and set currentRoute directly to .results\n3. Add error handling for partial success scenarios in processing\n4. Implement handleProcessingResults method to manage successful and failed processing results\n5. Update any navigation-related state management to ensure proper transitions\n6. Ensure that back navigation from results to staging works correctly\n7. Add support for processing overlay during Vision and Gemini processing", "testStrategy": "1. Test direct navigation from staging to results\n2. Verify that state is properly maintained during navigation\n3. Test error handling with various success/failure scenarios\n4. Test partial success handling to ensure usable results are displayed\n5. Test back navigation functionality\n6. Verify processing overlay appears and disappears appropriately"}, {"id": 4, "title": "Update State Management for Enhanced Processing Flow", "description": "Ensure proper state management during navigation transitions and processing flow, supporting up to 10 images and handling partial success scenarios.", "status": "done", "dependencies": [3], "details": "1. Update state management to handle up to 10 images in StagingView (increased from 3)\n2. Implement state tracking for Vision → Gemini processing pipeline\n3. Add error state handling for partial success scenarios\n4. Ensure proper state reset when navigating back from results to staging\n5. Implement processing mask state management during image analysis\n6. Handle edge cases like navigating with empty ingredient lists or during processing\n7. Add state indicators for processing progress", "testStrategy": "1. Test navigation with various image counts (1, 5, 10 images)\n2. Verify state is correctly maintained during processing pipeline\n3. Test error state handling with various failure scenarios\n4. Test state reset when navigating backward\n5. Verify processing indicators appear correctly\n6. Check for memory leaks during repeated navigation and processing\n7. Test edge cases like rapid navigation or cancellation during processing"}, {"id": 5, "title": "Update App Entry Points and Add Service Dependencies", "description": "Update app entry points and ensure Vision batch processing and Gemini normalization services are properly implemented as dependencies.", "status": "done", "dependencies": [1, 3], "details": "1. Identify all app entry points that might reference batch processing routes\n2. Update any deep link handlers to support both deprecated and new routes\n3. Ensure Vision batch processing service is implemented and available to StagingView\n4. Implement Gemini normalization service for ingredient processing\n5. Add service dependencies to StagingViewModel for complete processing pipeline\n6. Update any shortcuts or quick actions to use the new direct flow\n7. Ensure all entry points to the app navigate to valid routes\n8. Add error handling for service failures", "testStrategy": "1. Test app launch with various entry points\n2. Verify deep links correctly navigate to appropriate screens\n3. Test Vision batch processing service with multiple images\n4. Verify Gemini normalization service correctly processes ingredients\n5. Test service integration in the complete processing pipeline\n6. Test shortcuts and quick actions functionality\n7. Verify error handling for service failures"}]}, {"id": 29, "title": "Update StagingView and ViewModel for Single-Screen Experience", "description": "Modify StagingView and StagingViewModel to support a 10-image cap, two primary buttons, inline grid, and processing overlay.", "details": "1. Update StagingView to include two primary buttons: 'Scan Receipts and Ingredients' and 'Choose from Library'\n2. Implement image grid with 0-10 image capacity and per-image delete functionality\n3. Add Process button that appears when ≥1 image is present\n4. Create full-screen overlay with 'Processing...' message\n5. Ensure proper state management for image selection and processing states\n\n```swift\nclass StagingViewModel: ObservableObject {\n    @Published var selectedImages: [UIImage] = []\n    @Published var isProcessing = false\n    \n    // Maximum number of images allowed\n    let maxImages = 10\n    \n    // Check if Process button should be shown\n    var canProcess: Bool {\n        return !selectedImages.isEmpty\n    }\n    \n    // Add image if under limit\n    func addImage(_ image: UIImage) {\n        guard selectedImages.count < maxImages else { return }\n        selectedImages.append(image)\n    }\n    \n    // Remove image at index\n    func removeImage(at index: Int) {\n        guard selectedImages.indices.contains(index) else { return }\n        selectedImages.remove(at: index)\n    }\n    \n    // Clear all images\n    func clearImages() {\n        selectedImages.removeAll()\n    }\n}\n```", "testStrategy": "1. UI tests for camera and gallery button functionality\n2. Verify image grid displays correctly with 0-10 images\n3. Test image deletion functionality\n4. Confirm Process button appears only when at least one image is present\n5. Test overlay appearance during processing\n6. Verify max image limit enforcement", "priority": "high", "dependencies": [28], "status": "done", "subtasks": [{"id": 1, "title": "Implement Primary Buttons in StagingView", "description": "Update StagingView to include two primary buttons: 'Scan Receipts and Ingredients' and 'Choose from Library'. These buttons should be prominently displayed and trigger the appropriate image acquisition flows.", "dependencies": [], "details": "Create two primary buttons at the top of StagingView with appropriate styling. The 'Scan Receipts and Ingredients' button should open the camera for scanning, while 'Choose from Library' should open the photo picker. Add methods to StagingViewModel to handle these actions and wire them to the buttons. Ensure buttons are disabled during processing state.", "status": "done", "testStrategy": "Test UI appearance of buttons in different states. Verify button tap actions correctly trigger camera and photo library access. Test button disabled state during processing."}, {"id": 2, "title": "Create Image Grid with Delete Functionality", "description": "Implement a grid view that displays selected images with a maximum capacity of 10 images. Each image should have a delete button that allows removal of individual images.", "dependencies": ["29.1"], "details": "Create a LazyVGrid with appropriate spacing and sizing to display images in a grid format. Add a delete button overlay on each image that calls removeImage(at:) on the ViewModel. Implement empty state messaging when no images are present. Ensure grid is scrollable if needed and maintains proper layout with different numbers of images.", "status": "done", "testStrategy": "Test grid layout with various image counts (0, 1, 5, 10). Verify delete functionality removes the correct image. Test max capacity enforcement. Verify UI appearance and scrolling behavior."}, {"id": 3, "title": "Add Process Button with Conditional Display", "description": "Implement a Process button that appears only when at least one image is selected. The button should be prominently displayed below the image grid.", "dependencies": ["29.2"], "details": "Add a Process button below the image grid that is conditionally displayed based on the canProcess computed property in the ViewModel. Style the button appropriately to indicate it's the primary action. When tapped, the button should set isProcessing to true and trigger the image processing workflow.", "status": "done", "testStrategy": "Test button visibility with 0 images and with 1+ images. Verify button tap correctly sets isProcessing state. Test button appearance and positioning in the view hierarchy."}, {"id": 4, "title": "Create Processing Overlay", "description": "Implement a full-screen overlay with a 'Processing...' message and activity indicator that appears when processing is active.", "dependencies": ["29.3"], "details": "Create an overlay view that covers the entire screen with a semi-transparent background. Add a centered VStack containing an activity indicator (like ProgressView) and 'Processing...' text. The overlay should be conditionally displayed based on the isProcessing state in the ViewModel. Ensure the overlay prevents user interaction with elements behind it.", "status": "done", "testStrategy": "Test overlay appearance when isProcessing is true/false. Verify overlay blocks interaction with underlying UI elements. Test overlay dismissal when processing completes."}, {"id": 5, "title": "Implement State Management for Image Selection and Processing", "description": "Enhance StagingViewModel to properly manage state transitions between image selection, processing, and completion states.", "dependencies": ["29.4"], "details": "Add methods to StagingViewModel to handle state transitions: startProcessing(), completeProcessing(success:), and resetState(). Update isProcessing logic to prevent multiple processing requests. Add error handling for processing failures. Implement proper state reset after processing completes. Ensure all UI state updates are published correctly to update the view.", "status": "done", "testStrategy": "Test state transitions through the complete flow. Verify concurrent processing requests are handled properly. Test error states and recovery. Verify state resets correctly after processing completes or fails."}]}, {"id": 30, "title": "Implement GoogleVisionAPIService Batch Analysis", "description": "Create a batch analysis function in GoogleVisionAPIService that processes multiple images concurrently with OCR and label detection.", "details": "1. Add batchAnalyze method to GoogleVisionAPIService\n2. Implement concurrency control with a cap of 3-4 concurrent requests\n3. Add optional image resizing for images with longest side > 2000px\n4. Process both OCR and label detection for each image\n5. Return combined VisionOutput results\n\n```swift\nactor GoogleVisionAPIService {\n    // Existing methods...\n    \n    // New batch analysis method\n    func batchAnalyze(images: [UIImage]) async throws -> [VisionOutput] {\n        // Resize images if needed\n        let processableImages = images.map { resizeImageIfNeeded($0) }\n        \n        // Process with concurrency limit\n        return try await withTaskGroup(of: VisionOutput?.self) { group in\n            var results: [VisionOutput] = []\n            let concurrencyLimit = 3 // Cap at 3-4 concurrent requests\n            \n            // Add initial batch of tasks\n            for i in 0..<min(concurrencyLimit, processableImages.count) {\n                group.addTask { [weak self] in\n                    guard let self = self else { return nil }\n                    do {\n                        let ocrResult = try await self.performOCR(on: processableImages[i])\n                        let labelsResult = try await self.detectLabels(on: processableImages[i])\n                        return VisionOutput(ocrText: ocrResult, labels: labelsResult)\n                    } catch {\n                        return nil // Handle errors gracefully\n                    }\n                }\n            }\n            \n            // Process remaining images as tasks complete\n            var nextIndex = concurrencyLimit\n            while let result = await group.next() {\n                if let validResult = result {\n                    results.append(validResult)\n                }\n                \n                if nextIndex < processableImages.count {\n                    group.addTask { [weak self] in\n                        guard let self = self else { return nil }\n                        do {\n                            let ocrResult = try await self.performOCR(on: processableImages[nextIndex])\n                            let labelsResult = try await self.detectLabels(on: processableImages[nextIndex])\n                            return VisionOutput(ocrText: ocrResult, labels: labelsResult)\n                        } catch {\n                            return nil\n                        }\n                    }\n                    nextIndex += 1\n                }\n            }\n            \n            return results\n        }\n    }\n    \n    private func resizeImageIfNeeded(_ image: UIImage) -> UIImage {\n        let maxDimension: CGFloat = 2000\n        let width = image.size.width\n        let height = image.size.height\n        \n        // Check if resizing is needed\n        if width <= maxDimension && height <= maxDimension {\n            return image\n        }\n        \n        // Calculate new dimensions\n        let aspectRatio = width / height\n        var newWidth: CGFloat\n        var newHeight: CGFloat\n        \n        if width > height {\n            newWidth = maxDimension\n            newHeight = newWidth / aspectRatio\n        } else {\n            newHeight = maxDimension\n            newWidth = newHeight * aspectRatio\n        }\n        \n        // Resize image\n        let size = CGSize(width: newWidth, height: newHeight)\n        UIGraphicsBeginImageContextWithOptions(size, false, 1.0)\n        image.draw(in: CGRect(origin: .zero, size: size))\n        let resizedImage = UIGraphicsGetImageFromCurrentImageContext() ?? image\n        UIGraphicsEndImageContext()\n        \n        return resizedImage\n    }\n}\n```", "testStrategy": "1. Unit test batchAnalyze with various image counts\n2. Verify concurrency cap is respected\n3. Test image resizing functionality\n4. Mock Vision API responses to test error handling\n5. Measure performance with different batch sizes\n6. Verify correct VisionOutput structure is returned", "priority": "high", "dependencies": [28], "status": "done", "subtasks": [{"id": 1, "title": "Implement resizeImageIfNeeded function", "description": "Create a helper function that resizes images if their longest dimension exceeds 2000px to optimize processing time and memory usage.", "dependencies": [], "details": "Implement the resizeImageIfNeeded function that takes a UIImage as input and returns a resized version if needed. The function should check if either width or height exceeds 2000px, and if so, resize the image while maintaining the aspect ratio. Use UIGraphicsBeginImageContextWithOptions for the resizing operation and ensure proper memory management by ending the image context after creating the resized image.", "status": "done", "testStrategy": "Test with images of various dimensions (smaller than, equal to, and larger than the threshold). Verify that images below threshold remain unchanged. Confirm resized images maintain aspect ratio and have longest dimension of exactly 2000px."}, {"id": 2, "title": "Create VisionOutput struct", "description": "Define a VisionOutput struct to hold the combined results of OCR and label detection for each processed image.", "dependencies": [], "details": "Create a VisionOutput struct that contains two properties: ocrText (String) for the extracted text and labels (array of strings) for the detected labels. This struct will serve as the return type for both individual and batch image analysis operations. Implement any necessary initializers and make the struct Equatable for easier testing.", "status": "done", "testStrategy": "Verify struct correctly stores and retrieves OCR text and labels. Test equality comparison with identical and different VisionOutput instances."}, {"id": 3, "title": "Implement concurrent image processing logic", "description": "Create the core task group logic that processes multiple images concurrently while respecting the concurrency limit.", "dependencies": ["30.1", "30.2"], "details": "Implement the withTaskGroup portion of the batchAnalyze method that manages concurrent processing of images. Set up a task group of VisionOutput? type, add initial tasks up to the concurrency limit, and then dynamically add new tasks as previous ones complete. Maintain a results array to collect successful outputs and handle nil results appropriately. Ensure the concurrency limit of 3-4 concurrent requests is respected.", "status": "done", "testStrategy": "Test with varying numbers of images to verify concurrency control. Verify all images are processed. Test error handling by introducing mock failures. Measure performance to confirm concurrent processing is faster than sequential."}, {"id": 4, "title": "Implement individual image analysis in task group", "description": "Create the logic for processing a single image with both OCR and label detection within each task.", "dependencies": ["30.2", "30.3"], "details": "Implement the closure passed to each task in the task group that processes a single image. The closure should call the existing performOCR and detectLabels methods for the image, combine the results into a VisionOutput object, and handle any errors that occur during processing. Use weak self references to prevent memory leaks and return nil for failed operations to allow graceful degradation.", "status": "done", "testStrategy": "Test successful processing path returns correct VisionOutput. Verify error handling by mocking API failures. Test memory management with leak sanitizer to confirm no memory leaks from task closures."}, {"id": 5, "title": "Complete batchAnalyze method implementation", "description": "Finalize the batchAnalyze method by integrating all components and ensuring proper error handling and result collection.", "dependencies": ["30.1", "30.2", "30.3", "30.4"], "details": "Complete the batchAnalyze method by combining the image resizing, task group setup, and result collection logic. Ensure the method properly handles edge cases such as empty image arrays, all failed operations, and partial successes. Add appropriate error handling and propagation. Optimize the final implementation for performance and memory usage while maintaining the concurrency limit.", "status": "done", "testStrategy": "Test end-to-end functionality with various batch sizes. Verify correct results are returned for all images. Test edge cases including empty arrays and all-failure scenarios. Measure performance with realistic image sets to confirm efficiency gains from batch processing."}]}, {"id": 31, "title": "Implement GeminiAPIService Ingredient Canonicalization", "description": "Create a canonicalizeIngredients function in GeminiAPIService that processes Vision outputs and returns standardized ingredients with strict category enforcement.", "details": "1. Add canonicalizeIngredients method to GeminiAPIService\n2. Construct prompt with Vision outputs and allowed categories\n3. Enforce strict category membership from PantryCategory.rawValue\n4. Parse JSON response into Ingredient objects\n5. Handle error cases and invalid responses\n\n```swift\nactor GeminiAPIService {\n    // Existing methods...\n    \n    func canonicalizeIngredients(\n        visionOutputs: [VisionOutput],\n        allowedCategories: [String]\n    ) async throws -> [Ingredient] {\n        // Construct prompt with vision outputs and allowed categories\n        let prompt = buildCanonicalizePrompt(visionOutputs: visionOutputs, allowedCategories: allowedCategories)\n        \n        // Call Gemini API\n        let response = try await callGeminiAPI(prompt: prompt)\n        \n        // Parse response into ingredients\n        return try parseIngredientsResponse(response, allowedCategories: allowedCategories)\n    }\n    \n    private func buildCanonicalizePrompt(visionOutputs: [VisionOutput], allowedCategories: [String]) -> String {\n        var prompt = \"\"\"  \n        You are a food ingredient analyzer. Process the following OCR text and labels from images of food items, receipts, or packaging.\n        \n        For each detected item:\n        1. Extract the food ingredient name, removing brand names, sizes, quantities, and marketing text\n        2. Preserve meaningful descriptors (e.g., \"whole milk\" instead of just \"milk\")\n        3. Categorize each item into EXACTLY ONE of these categories: \\(allowedCategories.joined(separator: \", \"))\n        4. Exclude any non-food items or gibberish text entirely\n        \n        Return ONLY a JSON array with this format: [{\"name\":\"ingredient name\",\"category\":\"exact category\"}]\n        Do not include any explanation or additional text.\n        \"\"\"\n        \n        // Add vision outputs to prompt\n        for (index, output) in visionOutputs.enumerated() {\n            prompt += \"\\n\\nIMAGE \\(index + 1):\\nOCR TEXT: \\(output.ocrText)\\nLABELS: \\(output.labels.map { \"\\($0.text) (\\($0.confidence))\" }.joined(separator: \", \"))\"\n        }\n        \n        return prompt\n    }\n    \n    private func parseIngredientsResponse(_ response: String, allowedCategories: [String]) throws -> [Ingredient] {\n        // Extract JSON array from response\n        guard let jsonData = response.data(using: .utf8) else {\n            throw GeminiError.invalidResponse(\"Could not convert response to data\")\n        }\n        \n        // Decode JSON\n        struct GeminiIngredient: Decodable {\n            let name: String\n            let category: String\n        }\n        \n        let decoder = JSONDecoder()\n        let geminiIngredients = try decoder.decode([GeminiIngredient].self, from: jsonData)\n        \n        // Convert to Ingredient objects, filtering out invalid categories\n        return geminiIngredients.compactMap { geminiIngredient in\n            // Verify category is allowed\n            guard allowedCategories.contains(geminiIngredient.category) else {\n                return nil\n            }\n            \n            // Create Ingredient with valid category\n            guard let category = PantryCategory(rawValue: geminiIngredient.category) else {\n                return nil\n            }\n            \n            return Ingredient(\n                id: UUID(),\n                name: geminiIngredient.name,\n                category: category\n            )\n        }\n    }\n}\n\nenum GeminiError: Error {\n    case invalidResponse(String)\n    case apiError(String)\n}\n```", "testStrategy": "1. Unit test prompt construction with various vision outputs\n2. Test JSON parsing with valid and invalid responses\n3. Verify category enforcement against PantryCategory.rawValue\n4. Test error handling for malformed responses\n5. Verify non-food items are excluded\n6. Test with mock API responses to ensure correct ingredient extraction", "priority": "high", "dependencies": [30], "status": "done", "subtasks": [{"id": 1, "title": "Implement buildCanonicalizePrompt Method", "description": "Complete the implementation of the buildCanonicalizePrompt method that constructs a prompt for the Gemini API with vision outputs and allowed categories.", "dependencies": [], "details": "Finalize the buildCanonicalizePrompt method to properly format the prompt with clear instructions for the Gemini API. Ensure the prompt includes proper formatting of vision outputs (OCR text and labels) and clearly communicates the allowed categories. The prompt should emphasize the need for strict categorization and proper ingredient name extraction.", "status": "done", "testStrategy": "Test with various combinations of vision outputs to ensure proper prompt construction. Verify the prompt includes all necessary instructions and properly formats the vision data."}, {"id": 2, "title": "Implement parseIngredientsResponse Method", "description": "Complete the implementation of the parseIngredientsResponse method that converts the Gemini API response into Ingredient objects with strict category validation.", "dependencies": [], "details": "Implement robust JSON parsing in the parseIngredientsResponse method. Add proper error handling for malformed JSON responses. Ensure strict validation that each ingredient's category matches one of the allowed categories from PantryCategory.rawValue. Filter out any ingredients with invalid categories.", "status": "done", "testStrategy": "Test with various JSON responses including valid ingredients, invalid categories, malformed JSON, and empty responses. Verify that only ingredients with valid categories are returned."}, {"id": 3, "title": "Implement canonicalizeIngredients Method", "description": "Complete the implementation of the main canonicalizeIngredients method that orchestrates the API call process.", "dependencies": ["31.1", "31.2"], "details": "Implement the canonicalizeIngredients method to coordinate the process: build the prompt, call the Gemini API, and parse the response. Add proper error handling for API failures and invalid responses. Ensure the method correctly passes the allowed categories to both the prompt builder and response parser.", "status": "done", "testStrategy": "Test the full workflow with mock API responses. Verify proper error propagation and handling of edge cases like empty vision outputs or API failures."}, {"id": 4, "title": "Add Error Handling and Validation", "description": "Enhance error handling throughout the service to provide meaningful error messages and handle edge cases.", "dependencies": ["31.1", "31.2", "31.3"], "details": "Expand the GeminiError enum to cover additional error cases. Add validation for empty vision outputs and empty allowed categories. Implement proper error propagation with descriptive error messages. Add timeout handling for API calls. Ensure all error paths return appropriate GeminiError instances.", "status": "done", "testStrategy": "Test error handling with various failure scenarios including network errors, timeout errors, empty inputs, and malformed responses. Verify appropriate error messages are provided."}, {"id": 5, "title": "Implement Response Quality Checks", "description": "Add validation to ensure the quality of parsed ingredients before returning them.", "dependencies": ["31.2", "31.3"], "details": "Add post-processing validation to ensure ingredient names are properly formatted (trimmed, no excessive punctuation). Implement checks to filter out obviously invalid ingredients (too short, numeric-only, etc.). Add logging for rejected ingredients to help with debugging. Consider adding a minimum confidence threshold for accepting ingredients.", "status": "done", "testStrategy": "Test with various edge cases of ingredient names including very short names, names with excessive punctuation, and names that are clearly not food items. Verify the quality checks properly filter out invalid ingredients while keeping valid ones."}]}, {"id": 32, "title": "Wire Scan Pipeline in StagingViewModel", "description": "Connect Vision and Gemini services in StagingViewModel to create an end-to-end scanning pipeline with proper overlay management.", "details": "1. Update StagingViewModel to orchestrate the scanning pipeline\n2. Integrate GoogleVisionAPIService.batchAnalyze\n3. Pass Vision results to GeminiAPIService.canonicalizeIngredients\n4. Manage processing overlay lifecycle\n5. Handle navigation to Results with processed ingredients\n6. Implement error handling for Vision and Gemini failures\n\n```swift\nclass StagingViewModel: ObservableObject {\n    // Existing properties...\n    \n    @Published var isProcessing = false\n    @Published var processingError: Error? = nil\n    \n    private let visionService: GoogleVisionAPIService\n    private let geminiService: GeminiAPIService\n    private let navigationCoordinator: NavigationCoordinator\n    \n    init(visionService: GoogleVisionAPIService, geminiService: GeminiAPIService, navigationCoordinator: NavigationCoordinator) {\n        self.visionService = visionService\n        self.geminiService = geminiService\n        self.navigationCoordinator = navigationCoordinator\n    }\n    \n    func processImages() async {\n        guard !selectedImages.isEmpty else { return }\n        \n        // Show processing overlay\n        await MainActor.run { isProcessing = true }\n        \n        do {\n            // Step 1: Process images with Vision API\n            let visionOutputs = try await visionService.batchAnalyze(images: selectedImages)\n            \n            // Step 2: Process Vision outputs with Gemini\n            let allowedCategories = PantryCategory.allCases.map { $0.rawValue }\n            let ingredients = try await geminiService.canonicalizeIngredients(\n                visionOutputs: visionOutputs,\n                allowedCategories: allowedCategories\n            )\n            \n            // Step 3: Navigate to Results\n            await MainActor.run {\n                isProcessing = false\n                navigationCoordinator.navigateToResults(with: ingredients)\n            }\n        } catch {\n            // Handle errors\n            await MainActor.run {\n                isProcessing = false\n                processingError = error\n            }\n        }\n    }\n    \n    func restartScanning() {\n        selectedImages.removeAll()\n        processingError = nil\n    }\n}\n```", "testStrategy": "1. Unit test the full pipeline with mock services\n2. Verify overlay shows and hides at appropriate times\n3. Test error handling for Vision and Gemini failures\n4. Confirm navigation to Results occurs with correct data\n5. Test restart functionality clears state properly\n6. Measure performance with different image counts", "priority": "high", "dependencies": [29, 30, 31], "status": "done", "subtasks": [{"id": 1, "title": "Implement Image Processing with Vision API", "description": "Update StagingViewModel to process selected images with the GoogleVisionAPIService's batchAnalyze method, handling the initial stage of the scanning pipeline.", "dependencies": [], "details": "Modify the processImages() method to call visionService.batchAnalyze() with the selected images. Add proper error handling for Vision API failures and ensure the processing overlay is shown during this operation. Store the Vision API results for the next step in the pipeline. Update the method signature to include proper async/await handling.", "status": "done", "testStrategy": "Test with mock GoogleVisionAPIService to verify correct handling of successful responses and various error scenarios. Verify the processing overlay is shown during API calls and hidden on completion or error."}, {"id": 2, "title": "Integrate Gemini API for Ingredient Canonicalization", "description": "Pass Vision API results to GeminiAPIService for ingredient canonicalization, completing the second stage of the scanning pipeline.", "dependencies": ["32.1"], "details": "Extend the processImages() method to pass the Vision outputs to geminiService.canonicalizeIngredients(). Include the allowed categories from PantryCategory.allCases. Handle Gemini API errors appropriately and ensure the processing overlay remains visible during this operation. Store the canonicalized ingredients for navigation.", "status": "done", "testStrategy": "Test with mock GeminiAPIService to verify correct handling of successful responses and error scenarios. Test with various Vision output formats to ensure proper data transformation."}, {"id": 3, "title": "Implement Processing Overlay Management", "description": "Add proper lifecycle management for the processing overlay, ensuring it displays during API operations and hides appropriately on completion or error.", "dependencies": ["32.1", "32.2"], "details": "Ensure isProcessing state is properly managed throughout the pipeline. Set isProcessing = true at the start of processImages() and isProcessing = false when processing completes or fails. Use MainActor.run for all UI state updates to ensure thread safety. Add a timeout mechanism to prevent indefinite loading states.", "status": "done", "testStrategy": "Test the overlay visibility during different stages of processing. Verify overlay appears when processing starts and disappears when processing completes or fails. Test timeout mechanism to ensure overlay doesn't remain indefinitely."}, {"id": 4, "title": "Add Navigation to Results Screen", "description": "Implement navigation to the Results screen with processed ingredients upon successful completion of the scanning pipeline.", "dependencies": ["32.2", "32.3"], "details": "After successful processing with both Vision and Gemini APIs, call navigationCoordinator.navigateToResults(with: ingredients) to navigate to the Results screen with the processed ingredients. Ensure this happens on the main thread using MainActor.run. Clear the processing state before navigation.", "status": "done", "testStrategy": "Test with mock NavigationCoordinator to verify navigation occurs with the correct ingredient data. Verify navigation doesn't occur on errors. Test edge cases like empty ingredient lists."}, {"id": 5, "title": "<PERSON>han<PERSON>r Handling and Recovery", "description": "Implement comprehensive error handling for both Vision and Gemini API failures, with appropriate user feedback and recovery options.", "dependencies": ["32.1", "32.2", "32.3", "32.4"], "details": "Expand the catch block in processImages() to handle different error types (network errors, API errors, parsing errors). Update the processingError property with specific error information. Enhance the restartScanning() method to properly reset all state variables. Add specific recovery actions for different error types, such as retry options for network failures.", "status": "done", "testStrategy": "Test error handling with various error scenarios from both APIs. Verify appropriate error messages are displayed. Test the restartScanning() method to ensure it properly resets all state. Verify recovery actions work as expected for different error types."}]}, {"id": 33, "title": "Update ResultsView and ViewModel", "description": "Modify ResultsView and ResultsViewModel to support the new flow with checkbox selection, inline editing, and actions for adding to pantry or restarting scanning.", "details": "1. Update ResultsView to display ingredients grouped by category\n2. Implement checkbox selection for ingredients\n3. Add inline editing functionality (rename and recategorize)\n4. Add 'Add Selected to Pantry' and 'Restart Scanning' buttons\n5. Wire up actions to save to pantry and reset scan flow\n\n```swift\nclass ResultsViewModel: ObservableObject {\n    @Published var ingredients: [Ingredient] = []\n    @Published var selectedIngredientIds: Set<UUID> = []\n    @Published var editingIngredient: Ingredient? = nil\n    \n    private let pantryService: PantryService\n    private let navigationCoordinator: NavigationCoordinator\n    \n    init(ingredients: [Ingredient], pantryService: PantryService, navigationCoordinator: NavigationCoordinator) {\n        self.ingredients = ingredients\n        self.pantryService = pantryService\n        self.navigationCoordinator = navigationCoordinator\n        // Select all ingredients by default\n        self.selectedIngredientIds = Set(ingredients.map { $0.id })\n    }\n    \n    // Toggle selection for an ingredient\n    func toggleSelection(for ingredient: Ingredient) {\n        if selectedIngredientIds.contains(ingredient.id) {\n            selectedIngredientIds.remove(ingredient.id)\n        } else {\n            selectedIngredientIds.insert(ingredient.id)\n        }\n    }\n    \n    // Start editing an ingredient\n    func startEditing(ingredient: Ingredient) {\n        editingIngredient = ingredient\n    }\n    \n    // Update edited ingredient\n    func updateIngredient(id: UUID, name: String, category: PantryCategory) {\n        guard let index = ingredients.firstIndex(where: { $0.id == id }) else { return }\n        ingredients[index] = Ingredient(id: id, name: name, category: category)\n        editingIngredient = nil\n    }\n    \n    // Add selected ingredients to pantry\n    func addSelectedToPantry() async {\n        let selectedIngredients = ingredients.filter { selectedIngredientIds.contains($0.id) }\n        do {\n            try await pantryService.addIngredients(selectedIngredients)\n            // Navigate to Pantry tab\n            navigationCoordinator.switchToPantryTab()\n            // Reset scan flow\n            navigationCoordinator.resetScanFlow()\n        } catch {\n            // Handle error\n            print(\"Error adding to pantry: \\(error)\")\n        }\n    }\n    \n    // Restart scanning\n    func restartScanning() {\n        navigationCoordinator.resetScanFlow()\n    }\n    \n    // Get ingredients grouped by category\n    var ingredientsByCategory: [PantryCategory: [Ingredient]] {\n        Dictionary(grouping: ingredients) { $0.category }\n    }\n}\n```", "testStrategy": "1. UI tests for ingredient list display and grouping\n2. Test checkbox selection functionality\n3. Verify inline editing works for renaming and recategorizing\n4. Test 'Add Selected to Pantry' saves correct ingredients\n5. Confirm '<PERSON>art Scanning' resets the flow properly\n6. Test navigation to Pantry tab after saving", "priority": "medium", "dependencies": [28, 32], "status": "done", "subtasks": [{"id": 1, "title": "Implement grouped ingredient display in ResultsView", "description": "Update ResultsView to display ingredients grouped by category using the ingredientsByCategory computed property from the ViewModel.", "dependencies": [], "details": "Create a ForEach loop over the ingredientsByCategory dictionary to display ingredients grouped by their categories. Use a Section for each category with the category name as the header. Inside each section, create a list of ingredients belonging to that category. Ensure the UI is clean and consistent with the app's design language.", "status": "done", "testStrategy": "Verify ingredients appear grouped by category. Test with multiple categories and edge cases like empty categories or categories with single items."}, {"id": 2, "title": "Add checkbox selection functionality to ResultsView", "description": "Implement UI for checkbox selection of ingredients using the toggleSelection method from ResultsViewModel.", "dependencies": ["33.1"], "details": "Add a checkbox or toggle control next to each ingredient in the list. Bind the selection state to selectedIngredientIds in the ViewModel. Implement the tap gesture to call toggleSelection(for:) when a user taps the checkbox. Add visual indication of selected state. Remember that all ingredients should be selected by default as implemented in the ViewModel's init method.", "status": "done", "testStrategy": "Test selecting and deselecting ingredients. Verify the selectedIngredientIds set updates correctly. Test edge cases like selecting all or none."}, {"id": 3, "title": "Implement inline editing functionality", "description": "Create UI and logic for inline editing of ingredient names and categories using the startEditing and updateIngredient methods.", "dependencies": ["33.1"], "details": "Add an edit button or swipe action for each ingredient. When activated, call startEditing(ingredient:). Create an edit mode view that appears when editingIngredient is not nil, allowing users to modify the name and category. Implement a dropdown or picker for category selection. Add save and cancel buttons that either call updateIngredient with the new values or reset editingIngredient to nil.", "status": "done", "testStrategy": "Test editing flow from start to finish. Verify name and category changes persist in the ingredients array. Test cancellation of edits and validation of input."}, {"id": 4, "title": "Add action buttons for pantry and restart", "description": "Add 'Add Selected to Pantry' and 'Restart Scanning' buttons to the ResultsView with appropriate styling and positioning.", "dependencies": ["33.2"], "details": "Create two primary action buttons at the bottom of the view: 'Add Selected to Pantry' and 'Restart <PERSON>'. Style them according to app design guidelines, with the pantry action as the primary action. Disable the 'Add Selected to Pantry' button when no ingredients are selected. Position the buttons in a fixed position at the bottom of the screen with appropriate spacing and padding.", "status": "done", "testStrategy": "Verify buttons appear correctly and are enabled/disabled based on selection state. Test accessibility features like VoiceOver support."}, {"id": 5, "title": "Wire up action buttons to ViewModel methods", "description": "Connect the action buttons to their respective ViewModel methods and handle async operations properly.", "dependencies": ["33.4"], "details": "Connect the 'Add Selected to Pantry' button to the addSelectedToPantry() method using Task { await viewModel.addSelectedToPantry() } to handle the async operation. Add appropriate loading state during the pantry save operation. Connect the 'Restart Scanning' button to the restartScanning() method. Implement error handling for the pantry save operation, showing appropriate alerts or messages if errors occur.", "status": "done", "testStrategy": "Test the full flow of adding to pantry, including success and error scenarios. Verify navigation works correctly after adding to pantry. Test that restart scanning correctly resets the flow."}]}, {"id": 34, "title": "Update PantryService to Allow Duplicates", "description": "Modify PantryService to remove name normalization and allow duplicate ingredients when saving to the pantry.", "details": "1. Remove IngredientNameNormalizer usage from PantryService\n2. Update save methods to allow duplicate ingredients\n3. Ensure SwiftData persistence works correctly with duplicates\n4. Update related methods that might assume uniqueness\n\n```swift\nclass PantryService {\n    private let persistenceController: PersistenceController\n    \n    init(persistenceController: PersistenceController) {\n        self.persistenceController = persistenceController\n    }\n    \n    // Add ingredients to pantry (allowing duplicates)\n    func addIngredients(_ ingredients: [Ingredient]) async throws {\n        try await persistenceController.performTask { context in\n            for ingredient in ingredients {\n                // Create new ingredient without checking for duplicates\n                let pantryItem = PantryItem(context: context)\n                pantryItem.id = ingredient.id\n                pantryItem.name = ingredient.name // No normalization\n                pantryItem.category = ingredient.category.rawValue\n                pantryItem.createdAt = Date()\n            }\n            try context.save()\n        }\n    }\n    \n    // Get all pantry items\n    func getAllPantryItems() async throws -> [Ingredient] {\n        try await persistenceController.performTask { context in\n            let fetchRequest = NSFetchRequest<PantryItem>(entityName: \"PantryItem\")\n            let pantryItems = try context.fetch(fetchRequest)\n            \n            return pantryItems.map { item in\n                Ingredient(\n                    id: item.id,\n                    name: item.name, // No normalization on load\n                    category: PantryCategory(rawValue: item.category) ?? .other\n                )\n            }\n        }\n    }\n    \n    // Other methods...\n}\n```", "testStrategy": "1. Unit test adding duplicate ingredients\n2. Verify ingredients are saved without normalization\n3. Test retrieving pantry items returns exact names\n4. Confirm SwiftData persistence works with duplicates\n5. Test edge cases like empty names or special characters", "priority": "medium", "dependencies": [28], "status": "done", "subtasks": [{"id": 1, "title": "Remove IngredientNameNormalizer Usage", "description": "Remove all instances of IngredientNameNormalizer from PantryService to ensure ingredient names are stored exactly as provided without normalization.", "dependencies": [], "details": "1. Remove any imports for IngredientNameNormalizer\n2. Remove any instance variables or properties related to the normalizer\n3. Remove any calls to normalizer methods when processing ingredient names\n4. Ensure ingredient names are stored as-is in the addIngredients method\n5. Update any other methods that might be using the normalizer", "status": "done", "testStrategy": "Test adding ingredients with varied capitalization and spacing to verify they're stored without normalization. Compare input and output strings directly to ensure they match exactly."}, {"id": 2, "title": "Update Save Methods for Duplicates", "description": "Modify the save methods in PantryService to allow duplicate ingredients by removing any uniqueness checks or constraints.", "dependencies": ["34.1"], "details": "1. Review and update the addIngredients method to remove any code that checks for existing ingredients\n2. Remove any filtering or dictionary-based deduplication logic\n3. Ensure each ingredient is saved as a new PantryItem regardless of whether similar items exist\n4. Update any batch save methods to follow the same pattern\n5. Remove any unique constraints in the data model if they exist", "status": "done", "testStrategy": "Create test cases that add multiple ingredients with identical names and verify all are saved as separate entries. Count the number of items before and after to confirm duplicates are preserved."}, {"id": 3, "title": "Update Retrieval Methods for Duplicates", "description": "Update methods that retrieve pantry items to handle duplicates correctly and ensure they don't inadvertently deduplicate or filter results.", "dependencies": ["34.1", "34.2"], "details": "1. Review the getAllPantryItems method to ensure it returns all items including duplicates\n2. Update any grouping or filtering logic that might be removing duplicates\n3. Ensure any methods that search or filter pantry items handle duplicates appropriately\n4. Update any count or statistics methods to count duplicates as separate items\n5. Review sorting methods to ensure consistent ordering when duplicates exist", "status": "done", "testStrategy": "Test retrieving pantry items after adding duplicates and verify all items are returned. Test filtering and search functionality to ensure duplicates are handled correctly."}, {"id": 4, "title": "Ensure SwiftData Persistence with Duplicates", "description": "Verify and update the SwiftData/CoreData configuration to ensure it properly supports duplicate ingredients without unique constraints.", "dependencies": ["34.2"], "details": "1. Review the PantryItem entity definition in the data model\n2. Remove any unique constraints on the name property if they exist\n3. Update any indexes that might enforce uniqueness\n4. Verify fetch requests don't include deduplication logic\n5. Test persistence operations with duplicate items to ensure they're saved and retrieved correctly", "status": "done", "testStrategy": "Create a comprehensive test that adds multiple identical ingredients, persists them to storage, restarts the app context, and verifies all duplicates are retrieved correctly from persistent storage."}, {"id": 5, "title": "Update UI Handling for Duplicate Ingredients", "description": "Update any UI components or view models that might assume ingredient uniqueness to properly display and manage duplicate ingredients.", "dependencies": ["34.3"], "details": "1. Review any list views that display pantry items to ensure they can show duplicates\n2. Update item identification to use ID rather than name for uniqueness\n3. Ensure selection mechanisms in UI can distinguish between duplicates\n4. Update any count displays or statistics to account for duplicates\n5. Review deletion and editing functionality to ensure it targets the correct item when duplicates exist", "status": "done", "testStrategy": "Test the UI with duplicate ingredients to verify they display correctly. Test selecting, editing, and deleting duplicate items to ensure the operations affect only the intended item."}]}, {"id": 35, "title": "Create PantryOrganizerService", "description": "Implement a new PantryOrganizerService that can clean up the entire pantry by fixing categories, removing non-food items, and merging duplicates.", "details": "1. Create PantryOrganizerService with organize and apply methods\n2. Implement Gemini prompt for pantry organization\n3. Process pantry items in batches (100-200 per request)\n4. Parse and apply CleanUpPlan to update/remove/merge items\n5. Generate summary of changes\n\n```swift\nactor PantryOrganizerService {\n    private let geminiService: GeminiAPIService\n    private let pantryService: PantryService\n    private let batchSize = 150 // Process 100-200 items per batch\n    \n    init(geminiService: GeminiAPIService, pantryService: PantryService) {\n        self.geminiService = geminiService\n        self.pantryService = pantryService\n    }\n    \n    func organize(items: [Ingredient], allowedCategories: [String]) async throws -> CleanUpPlan {\n        // Process in batches to respect token limits\n        var fullPlan = CleanUpPlan(updates: [], removals: [], merges: [])\n        \n        // Split items into batches\n        let batches = stride(from: 0, to: items.count, by: batchSize).map {\n            Array(items[$0..<min($0 + batchSize, items.count)])\n        }\n        \n        // Process each batch\n        for batch in batches {\n            let batchPlan = try await processBatch(items: batch, allowedCategories: allowedCategories)\n            fullPlan.updates.append(contentsOf: batchPlan.updates)\n            fullPlan.removals.append(contentsOf: batchPlan.removals)\n            fullPlan.merges.append(contentsOf: batchPlan.merges)\n        }\n        \n        return fullPlan\n    }\n    \n    private func processBatch(items: [Ingredient], allowedCategories: [String]) async throws -> CleanUpPlan {\n        // Build prompt for this batch\n        let prompt = buildOrganizerPrompt(items: items, allowedCategories: allowedCategories)\n        \n        // Call Gemini API\n        let response = try await geminiService.callGeminiAPI(prompt: prompt)\n        \n        // Parse response into CleanUpPlan\n        return try parseCleanUpPlan(response)\n    }\n    \n    private func buildOrganizerPrompt(items: [Ingredient], allowedCategories: [String]) -> String {\n        var prompt = \"\"\"  \n        You are a pantry organization assistant. Clean up the following pantry items by:\n        1. Fixing categories to match exactly one of these allowed categories: \\(allowedCategories.joined(separator: \", \"))\n        2. Cleaning names by removing brand names, sizes, quantities, and marketing text\n        3. Preserving meaningful descriptors (e.g., \"whole milk\" instead of just \"milk\")\n        4. Identifying non-food items or gibberish for removal\n        5. Identifying duplicate items that should be merged\n        \n        For each item, return one of:\n        - Update: {\"id\": \"[original id]\", \"newName\": \"[cleaned name]\", \"newCategory\": \"[exact category]\"}\n        - Remove: {\"id\": \"[original id]\", \"remove\": true}\n        - For duplicates, suggest merges: {\"winnerId\": \"[id to keep]\", \"loserIds\": [\"id1\", \"id2\", ...]}\n        \n        Return ONLY a JSON object with this format: {\"updates\": [...], \"removals\": [...], \"merges\": [...]}\n        Do not include any explanation or additional text.\n        \"\"\"\n        \n        // Add items to prompt\n        prompt += \"\\n\\nPANTRY ITEMS:\\n\"\n        for item in items {\n            prompt += \"{\\\"id\\\": \\\"\\(item.id)\\\", \\\"name\\\": \\\"\\(item.name)\\\", \\\"category\\\": \\\"\\(item.category.rawValue)\\\"}\\n\"\n        }\n        \n        return prompt\n    }\n    \n    private func parseCleanUpPlan(_ response: String) throws -> CleanUpPlan {\n        // Extract JSON from response\n        guard let jsonData = response.data(using: .utf8) else {\n            throw OrganizerError.invalidResponse(\"Could not convert response to data\")\n        }\n        \n        // Decode JSON\n        let decoder = JSONDecoder()\n        return try decoder.decode(CleanUpPlan.self, from: jsonData)\n    }\n    \n    func apply(plan: CleanUpPlan) async throws -> PantryOrganizerSummary {\n        var summary = PantryOrganizerSummary(updatedCount: 0, removedCount: 0, mergedCount: 0)\n        \n        // Apply updates\n        for update in plan.updates {\n            try await pantryService.updateIngredient(id: update.id, name: update.newName, category: update.newCategory)\n            summary.updatedCount += 1\n        }\n        \n        // Apply removals\n        for id in plan.removals {\n            try await pantryService.removeIngredient(id: id)\n            summary.removedCount += 1\n        }\n        \n        // Apply merges\n        for merge in plan.merges {\n            try await pantryService.mergeIngredients(winnerId: merge.winnerId, loserIds: merge.loserIds)\n            summary.mergedCount += merge.loserIds.count\n        }\n        \n        return summary\n    }\n}\n\nenum OrganizerError: Error {\n    case invalidResponse(String)\n    case applicationError(String)\n}\n```", "testStrategy": "✅ **COMPLETED** - All testing requirements met:\n1. ✅ Unit test batch processing with various pantry sizes\n2. ✅ Test prompt construction with different item sets\n3. ✅ Verify JSON parsing for CleanUpPlan\n4. ✅ Test apply method with mock PantryService\n5. ✅ Verify summary generation is accurate\n6. ✅ Test error handling for API failures and invalid responses\n\n**Build Status:** ✅ SUCCESS - Project compiles without errors\n**Integration Status:** ✅ SUCCESS - All services properly integrated", "priority": "high", "dependencies": [31, 34], "status": "done", "subtasks": [{"id": 1, "title": "Implement PantryOrganizerService Core Structure", "description": "Create the basic actor structure for PantryOrganizerService with initialization and required dependencies.", "dependencies": [], "details": "✅ **COMPLETED** - Successfully created PantryOrganizerService actor with proper initialization, batchSize constant, and OrganizerError enum definition.", "status": "done", "testStrategy": "Test initialization with mock dependencies. Verify actor properties are correctly set."}, {"id": 2, "title": "Implement Batch Processing Logic", "description": "Create the logic to split pantry items into manageable batches and process them sequentially.", "dependencies": ["35.1"], "details": "✅ **COMPLETED** - Successfully implemented organize method with batch splitting logic and processBatch method that combines results from all batches into a single CleanUpPlan object.", "status": "done", "testStrategy": "Test with various array sizes to verify correct batch splitting. Verify that results from multiple batches are properly combined."}, {"id": 3, "title": "Implement Gemini Prompt Construction", "description": "Create the buildOrganizerPrompt method that constructs the AI prompt for pantry organization.", "dependencies": ["35.1"], "details": "✅ **COMPLETED** - Successfully implemented buildOrganizerPrompt method that formats ingredient data into structured AI prompts for pantry organization with proper JSON output formatting.", "status": "done", "testStrategy": "Test with various ingredient lists and category sets to verify prompt construction. Check that special characters in ingredient names are properly escaped."}, {"id": 4, "title": "Implement Response Parsing Logic", "description": "Create the parseCleanUpPlan method to convert Gemini API responses into CleanUpPlan objects.", "dependencies": ["35.1"], "details": "✅ **COMPLETED** - Successfully implemented parseCleanUpPlan method with robust JSON parsing, error handling for invalid responses, and support for the expected JSON structure with updates, removals, and merges arrays.", "status": "done", "testStrategy": "Test with various JSON response formats, including valid responses, malformed JSON, and empty responses. Verify error handling for invalid responses."}, {"id": 5, "title": "Implement Plan Application Logic", "description": "Create the apply method to execute the CleanUpPlan on the pantry data.", "dependencies": ["35.1", "35.4"], "details": "✅ **COMPLETED** - Successfully implemented apply method that executes CleanUpPlan changes through PantryService, processes updates/removals/merges sequentially, tracks operation counts, and returns PantryOrganizerSummary with final statistics.", "status": "done", "testStrategy": "Test with various CleanUpPlan configurations. Verify that PantryService methods are called with correct parameters. Test error handling when PantryService operations fail. Verify summary counts match the operations performed."}]}, {"id": 36, "title": "Implement Pantry Organization UI", "description": "Add 'Organize Pantry' button to the Pantry tab and implement UI for showing organization progress and summary.", "details": "1. Add 'Organize Pantry' button to PantryView toolbar\n2. Create overlay for 'Analyzing pantry...' during processing\n3. Implement summary view showing counts of cleaned/merged/removed items\n4. Connect UI to PantryOrganizerService\n5. Handle error states and provide retry option\n\n```swift\nclass PantryViewModel: ObservableObject {\n    // Existing properties...\n    \n    @Published var isOrganizing = false\n    @Published var organizerSummary: PantryOrganizerSummary? = nil\n    @Published var organizerError: Error? = nil\n    \n    private let pantryService: PantryService\n    private let organizerService: PantryOrganizerService\n    \n    init(pantryService: PantryService, organizerService: PantryOrganizerService) {\n        self.pantryService = pantryService\n        self.organizerService = organizerService\n        // Other initialization...\n    }\n    \n    func organizePantry() async {\n        // Reset state\n        await MainActor.run {\n            isOrganizing = true\n            organizerSummary = nil\n            organizerError = nil\n        }\n        \n        do {\n            // Get all pantry items\n            let items = try await pantryService.getAllPantryItems()\n            \n            // Get allowed categories\n            let allowedCategories = PantryCategory.allCases.map { $0.rawValue }\n            \n            // Generate cleanup plan\n            let plan = try await organizerService.organize(items: items, allowedCategories: allowedCategories)\n            \n            // Apply plan\n            let summary = try await organizerService.apply(plan: plan)\n            \n            // Update UI with summary\n            await MainActor.run {\n                isOrganizing = false\n                organizerSummary = summary\n                // Refresh pantry items\n                loadPantryItems()\n            }\n        } catch {\n            await MainActor.run {\n                isOrganizing = false\n                organizerError = error\n            }\n        }\n    }\n    \n    func dismissSummary() {\n        organizerSummary = nil\n    }\n    \n    func retryOrganization() async {\n        organizerError = nil\n        await organizePantry()\n    }\n}\n```\n\n```swift\nstruct PantryView: View {\n    @ObservedObject var viewModel: PantryViewModel\n    \n    var body: some View {\n        ZStack {\n            // Existing pantry view content...\n            \n            // Organizing overlay\n            if viewModel.isOrganizing {\n                VStack {\n                    ProgressView()\n                        .padding()\n                    Text(\"Analyzing pantry...\")\n                        .font(.headline)\n                }\n                .frame(maxWidth: .infinity, maxHeight: .infinity)\n                .background(Color.black.opacity(0.5))\n                .edgesIgnoringSafeArea(.all)\n            }\n            \n            // Summary sheet\n            if let summary = viewModel.organizerSummary {\n                VStack(spacing: 16) {\n                    Text(\"Pantry Organization Complete\")\n                        .font(.headline)\n                    \n                    VStack(alignment: .leading, spacing: 8) {\n                        Text(\"Cleaned: \\(summary.updatedCount) items\")\n                        Text(\"Merged: \\(summary.mergedCount) duplicates\")\n                        Text(\"Removed: \\(summary.removedCount) non-food items\")\n                    }\n                    \n                    Button(\"Done\") {\n                        viewModel.dismissSummary()\n                    }\n                    .buttonStyle(.borderedProminent)\n                }\n                .padding()\n                .background(Color(.systemBackground))\n                .cornerRadius(12)\n                .shadow(radius: 5)\n                .padding()\n            }\n            \n            // Error view\n            if let error = viewModel.organizerError {\n                VStack(spacing: 16) {\n                    Text(\"Organization Error\")\n                        .font(.headline)\n                    \n                    Text(error.localizedDescription)\n                        .multilineTextAlignment(.center)\n                    \n                    Button(\"Try Again\") {\n                        Task {\n                            await viewModel.retryOrganization()\n                        }\n                    }\n                    .buttonStyle(.borderedProminent)\n                    \n                    Button(\"Cancel\") {\n                        viewModel.organizerError = nil\n                    }\n                    .buttonStyle(.bordered)\n                }\n                .padding()\n                .background(Color(.systemBackground))\n                .cornerRadius(12)\n                .shadow(radius: 5)\n                .padding()\n            }\n        }\n        .toolbar {\n            ToolbarItem(placement: .primaryAction) {\n                Button(\"Organize Pantry\") {\n                    Task {\n                        await viewModel.organizePantry()\n                    }\n                }\n                .disabled(viewModel.isOrganizing)\n            }\n            \n            // Other toolbar items...\n        }\n    }\n}\n```", "testStrategy": "1. UI tests for 'Organize Pantry' button functionality\n2. Verify overlay appears during processing\n3. Test summary display with various result counts\n4. Confirm error handling and retry functionality\n5. Test accessibility of all UI elements\n6. Verify UI state after organization completes", "priority": "medium", "dependencies": [35], "status": "completed", "subtasks": [{"id": 1, "title": "Add 'Organize Pantry' button to PantryView toolbar", "description": "Implement the 'Organize Pantry' button in the toolbar of PantryView and connect it to the organizePantry() function in the view model.", "dependencies": [], "details": "Add a ToolbarItem with placement .primaryAction to the PantryView's toolbar. Create a <PERSON><PERSON> with the text 'Organize Pantry' that calls viewModel.organizePantry() asynchronously using Task. Ensure the button is disabled when viewModel.isOrganizing is true to prevent multiple simultaneous organization attempts.", "status": "pending", "testStrategy": "Verify button appears in toolbar, test button disabled state when isOrganizing is true, and confirm the button triggers organizePantry() when tapped."}, {"id": 2, "title": "Implement processing overlay with progress indicator", "description": "Create an overlay that displays a progress indicator and 'Analyzing pantry...' text when the pantry organization is in progress.", "dependencies": ["36.1"], "details": "Add a ZStack to PantryView that conditionally shows an overlay when viewModel.isOrganizing is true. The overlay should contain a ProgressView and a Text view with 'Analyzing pantry...' message. Apply a semi-transparent black background (opacity 0.5) that covers the entire screen and ignores safe area edges. Ensure the overlay is centered and blocks interaction with the underlying content.", "status": "pending", "testStrategy": "Test that overlay appears when isOrganizing is set to true, verify overlay content is correctly displayed, and confirm overlay disappears when isOrganizing becomes false."}, {"id": 3, "title": "Create organization summary view", "description": "Implement a summary view that displays the results of the pantry organization process, showing counts of cleaned, merged, and removed items.", "dependencies": ["36.2"], "details": "Add a conditional view in the ZStack that appears when viewModel.organizer<PERSON><PERSON><PERSON><PERSON> is not nil. Create a VStack with a headline text 'Pantry Organization Complete', followed by three Text views showing the counts from the summary (cleaned, merged, removed items). Add a 'Done' button that calls viewModel.dismissSummary(). Style the container with padding, background color, corner radius, and shadow for a card-like appearance.", "status": "pending", "testStrategy": "Test summary view appears when organizer<PERSON><PERSON><PERSON><PERSON> is set, verify correct display of count information, and confirm the Done button dismisses the summary view."}, {"id": 4, "title": "Implement error handling UI", "description": "Create an error view that displays when the organization process encounters an error, showing the error message and providing retry and cancel options.", "dependencies": ["36.2"], "details": "Add a conditional view in the ZStack that appears when viewModel.organizerError is not nil. Create a VStack with a headline text 'Organization Error', followed by a Text view displaying the error's localized description. Add two buttons: 'Try Again' that calls viewModel.retryOrganization() asynchronously, and 'Cancel' that sets viewModel.organizerError to nil. Style the container similar to the summary view with appropriate padding, background, corner radius, and shadow.", "status": "pending", "testStrategy": "Test error view appears when organizer<PERSON><PERSON><PERSON> is set, verify error message is displayed correctly, confirm retry button calls retryOrganization(), and test cancel button clears the error state."}, {"id": 5, "title": "Connect UI to PantryOrganizerService", "description": "Ensure the PantryViewModel correctly interacts with the PantryOrganizerService to perform the organization process and update the UI accordingly.", "dependencies": ["36.1", "36.2", "36.3", "36.4"], "details": "Review and finalize the organizePantry() method in PantryViewModel to ensure it properly: 1) Sets isOrganizing to true at start, 2) Retrieves pantry items from pantryService, 3) Gets allowed categories from PantryCategory enum, 4) Calls organizerService.organize() and organizerService.apply(), 5) Updates UI with the summary on success, 6) Handles errors appropriately, and 7) Ensures all UI updates happen on the main thread using MainActor. Also verify the dismissSummary() and retryOrganization() methods work correctly.", "status": "pending", "testStrategy": "Test the full organization flow from button press to summary display, verify error handling works when service throws exceptions, confirm UI state transitions occur correctly, and test that pantry items are refreshed after successful organization."}]}, {"id": 37, "title": "Implement Error Handling for Scan Pipeline", "description": "Add robust error handling for the scanning pipeline to handle Vision and Gemini API failures gracefully.", "details": "1. Create error types for different failure scenarios\n2. Update StagingViewModel to handle partial Vision successes\n3. Implement error UI in StagingView\n4. Add retry functionality for failed operations\n5. Ensure state consistency during error recovery\n\n```swift\nenum ScanError: Error, LocalizedError {\n    case visionError(String)\n    case geminiError(String)\n    case partialVisionSuccess(successCount: Int, totalCount: Int)\n    case networkError(String)\n    \n    var errorDescription: String? {\n        switch self {\n        case .visionError(let message):\n            return \"Image analysis error: \\(message)\"\n        case .geminiError(let message):\n            return \"Text processing error: \\(message)\"\n        case .partialVisionSuccess(let successCount, let totalCount):\n            return \"Processed \\(successCount) of \\(totalCount) images successfully\"\n        case .networkError(let message):\n            return \"Network error: \\(message)\"\n        }\n    }\n    \n    var recoverySuggestion: String? {\n        switch self {\n        case .visionError, .geminiError, .networkError:\n            return \"Please try again or use fewer images\"\n        case .partialVisionSuccess:\n            return \"Some images couldn't be processed. You can continue with partial results or try again\"\n        }\n    }\n}\n\nextension StagingViewModel {\n    func processImages() async {\n        guard !selectedImages.isEmpty else { return }\n        \n        await MainActor.run { \n            isProcessing = true \n            processingError = nil\n        }\n        \n        do {\n            // Process images with Vision API\n            var visionOutputs: [VisionOutput] = []\n            var failedImageCount = 0\n            \n            // Try to process each image individually to handle partial failures\n            for image in selectedImages {\n                do {\n                    let output = try await visionService.analyzeImage(image)\n                    visionOutputs.append(output)\n                } catch {\n                    failedImageCount += 1\n                    // Continue with other images\n                }\n            }\n            \n            // Check if we have at least some results\n            if visionOutputs.isEmpty {\n                throw ScanError.visionError(\"Could not process any images\")\n            }\n            \n            // Note partial success\n            if failedImageCount > 0 {\n                // Continue with partial results but note the issue\n                print(\"Processed \\(visionOutputs.count) of \\(selectedImages.count) images successfully\")\n            }\n            \n            // Process Vision outputs with Gemini\n            let allowedCategories = PantryCategory.allCases.map { $0.rawValue }\n            let ingredients = try await geminiService.canonicalizeIngredients(\n                visionOutputs: visionOutputs,\n                allowedCategories: allowedCategories\n            )\n            \n            await MainActor.run {\n                isProcessing = false\n                \n                // If we had partial success, note it in the error but still show results\n                if failedImageCount > 0 {\n                    processingError = ScanError.partialVisionSuccess(\n                        successCount: visionOutputs.count,\n                        totalCount: selectedImages.count\n                    )\n                }\n                \n                // Navigate to Results with the ingredients we could extract\n                if !ingredients.isEmpty {\n                    navigationCoordinator.navigateToResults(with: ingredients)\n                } else {\n                    processingError = ScanError.geminiError(\"No ingredients could be identified\")\n                }\n            }\n        } catch let error as ScanError {\n            await MainActor.run {\n                isProcessing = false\n                processingError = error\n            }\n        } catch {\n            await MainActor.run {\n                isProcessing = false\n                processingError = ScanError.networkError(error.localizedDescription)\n            }\n        }\n    }\n}\n```", "testStrategy": "1. Unit test error handling for different failure scenarios\n2. Test partial success handling with mixed success/failure images\n3. Verify error messages are user-friendly\n4. Test retry functionality after errors\n5. Confirm state consistency during error recovery\n6. Test network error handling", "priority": "medium", "dependencies": [32], "status": "pending", "subtasks": []}, {"id": 38, "title": "Implement Error Handling for Pantry Organization", "description": "Add robust error handling for the pantry organization process to handle API failures and batch processing errors.", "details": "1. Create error types for organization failures\n2. Update PantryOrganizerService to handle batch failures gracefully\n3. Implement partial success handling for batch processing\n4. Add retry functionality for failed batches\n5. Ensure data consistency during error recovery\n\n```swift\nenum OrganizerError: Error, LocalizedError {\n    case apiError(String)\n    case parseError(String)\n    case applicationError(String)\n    case partialBatchSuccess(processedCount: Int, totalCount: Int)\n    \n    var errorDescription: String? {\n        switch self {\n        case .apiError(let message):\n            return \"API error: \\(message)\"\n        case .parseError(let message):\n            return \"Could not process response: \\(message)\"\n        case .applicationError(let message):\n            return \"Application error: \\(message)\"\n        case .partialBatchSuccess(let processedCount, let totalCount):\n            return \"Processed \\(processedCount) of \\(totalCount) items successfully\"\n        }\n    }\n    \n    var recoverySuggestion: String? {\n        switch self {\n        case .apiError, .parseError:\n            return \"Please try again later\"\n        case .applicationError:\n            return \"Please try again or contact support\"\n        case .partialBatchSuccess:\n            return \"Some items couldn't be processed. You can continue with partial results or try again\"\n        }\n    }\n}\n\nextension PantryOrganizerService {\n    func organize(items: [Ingredient], allowedCategories: [String]) async throws -> CleanUpPlan {\n        var fullPlan = CleanUpPlan(updates: [], removals: [], merges: [])\n        var processedCount = 0\n        var failedBatches = 0\n        \n        // Split items into batches\n        let batches = stride(from: 0, to: items.count, by: batchSize).map {\n            Array(items[$0..<min($0 + batchSize, items.count)])\n        }\n        \n        // Process each batch\n        for batch in batches {\n            do {\n                let batchPlan = try await processBatch(items: batch, allowedCategories: allowedCategories)\n                fullPlan.updates.append(contentsOf: batchPlan.updates)\n                fullPlan.removals.append(contentsOf: batchPlan.removals)\n                fullPlan.merges.append(contentsOf: batchPlan.merges)\n                processedCount += batch.count\n            } catch {\n                failedBatches += 1\n                // Continue with other batches\n            }\n        }\n        \n        // Check if we have at least some results\n        if processedCount == 0 {\n            throw OrganizerError.apiError(\"Could not process any items\")\n        }\n        \n        // Note partial success\n        if failedBatches > 0 {\n            print(\"Processed \\(processedCount) of \\(items.count) items successfully\")\n            throw OrganizerError.partialBatchSuccess(processedCount: processedCount, totalCount: items.count)\n        }\n        \n        return fullPlan\n    }\n    \n    func apply(plan: CleanUpPlan) async throws -> PantryOrganizerSummary {\n        var summary = PantryOrganizerSummary(updatedCount: 0, removedCount: 0, mergedCount: 0)\n        \n        // Apply updates in batches for atomicity\n        let updateBatches = stride(from: 0, to: plan.updates.count, by: 50).map {\n            Array(plan.updates[$0..<min($0 + 50, plan.updates.count)])\n        }\n        \n        for batch in updateBatches {\n            do {\n                try await pantryService.batchUpdateIngredients(updates: batch)\n                summary.updatedCount += batch.count\n            } catch {\n                // Log error but continue with other operations\n                print(\"Error applying update batch: \\(error)\")\n            }\n        }\n        \n        // Apply removals\n        do {\n            try await pantryService.batchRemoveIngredients(ids: plan.removals)\n            summary.removedCount += plan.removals.count\n        } catch {\n            print(\"Error applying removals: \\(error)\")\n        }\n        \n        // Apply merges one by one (more complex operation)\n        for merge in plan.merges {\n            do {\n                try await pantryService.mergeIngredients(winnerId: merge.winnerId, loserIds: merge.loserIds)\n                summary.mergedCount += merge.loserIds.count\n            } catch {\n                print(\"Error applying merge: \\(error)\")\n            }\n        }\n        \n        return summary\n    }\n}\n```", "testStrategy": "1. Unit test batch processing error handling\n2. Test partial success scenarios with mixed success/failure batches\n3. Verify error messages are user-friendly\n4. Test retry functionality after batch failures\n5. Confirm data consistency during error recovery\n6. Test API error handling", "priority": "medium", "dependencies": [35], "status": "pending", "subtasks": []}, {"id": 39, "title": "Implement Accessibility Features", "description": "Enhance the app with accessibility features to ensure it's usable by people with disabilities.", "details": "1. Add proper accessibility labels and hints to all UI elements\n2. Ensure proper VoiceOver support for processing overlays\n3. Implement high-contrast labels and buttons\n4. Add accessibility traits to interactive elements\n5. Test with VoiceOver and other accessibility features\n\n```swift\n// Example accessibility enhancements for StagingView\nextension StagingView {\n    func configureAccessibility() {\n        // Camera button\n        cameraButton\n            .accessibilityLabel(\"Scan Receipts and Ingredients\")\n            .accessibilityHint(\"Takes a photo of receipts or food packaging to add to your pantry\")\n        \n        // Gallery button\n        galleryButton\n            .accessibilityLabel(\"Choose from Library\")\n            .accessibilityHint(\"Select photos from your library to add to your pantry\")\n        \n        // Process button\n        processButton\n            .accessibilityLabel(\"Process Images\")\n            .accessibilityHint(\"Analyzes selected images to extract ingredients\")\n            .accessibilityTraits(isProcessing ? [.button, .notEnabled] : .button)\n        \n        // Processing overlay\n        if isProcessing {\n            VStack {\n                Text(\"Processing...\")\n                    .accessibilityLabel(\"Processing images\")\n                    .accessibilityTraits(.updatesFrequently)\n            }\n            .accessibilityElement(children: .combine)\n            .accessibilityLabel(\"Processing images, please wait\")\n        }\n        \n        // Image grid\n        ForEach(selectedImages.indices, id: \\.self) { index in\n            imageView(for: selectedImages[index], at: index)\n                .accessibilityLabel(\"Image \\(index + 1) of \\(selectedImages.count)\")\n                .accessibilityHint(\"Double tap to remove this image\")\n                .accessibilityAction {\n                    viewModel.removeImage(at: index)\n                }\n        }\n    }\n}\n\n// Example accessibility enhancements for ResultsView\nextension ResultsView {\n    func configureAccessibility() {\n        // Category sections\n        ForEach(viewModel.ingredientsByCategory.keys.sorted(), id: \\.self) { category in\n            Section(header: Text(category.rawValue)) {\n                ForEach(viewModel.ingredientsByCategory[category] ?? []) { ingredient in\n                    ingredientRow(for: ingredient)\n                        .accessibilityElement(children: .combine)\n                        .accessibilityLabel(\"\\(ingredient.name), \\(ingredient.category.rawValue)\")\n                        .accessibilityValue(viewModel.isSelected(ingredient) ? \"Selected\" : \"Not selected\")\n                        .accessibilityHint(\"Double tap to toggle selection, swipe up for more actions\")\n                        .accessibilityAction(.default) {\n                            viewModel.toggleSelection(for: ingredient)\n                        }\n                        .accessibilityAction(named: \"Edit\") {\n                            viewModel.startEditing(ingredient: ingredient)\n                        }\n                }\n            }\n            .accessibilityLabel(\"\\(category.rawValue) category with \\(viewModel.ingredientsByCategory[category]?.count ?? 0) items\")\n        }\n        \n        // Action buttons\n        addToPantryButton\n            .accessibilityLabel(\"Add Selected to Pantry\")\n            .accessibilityHint(\"Saves selected ingredients to your pantry\")\n        \n        restartButton\n            .accessibilityLabel(\"Restart Scanning\")\n            .accessibilityHint(\"Clears current results and returns to scanning\")\n    }\n}\n\n// Example accessibility enhancements for PantryView\nextension PantryView {\n    func configureAccessibility() {\n        // Organize button\n        organizeButton\n            .accessibilityLabel(\"Organize Pantry\")\n            .accessibilityHint(\"Cleans up your pantry by fixing categories and merging duplicates\")\n            .accessibilityTraits(viewModel.isOrganizing ? [.button, .notEnabled] : .button)\n        \n        // Organizing overlay\n        if viewModel.isOrganizing {\n            VStack {\n                Text(\"Analyzing pantry...\")\n                    .accessibilityLabel(\"Analyzing pantry\")\n                    .accessibilityTraits(.updatesFrequently)\n            }\n            .accessibilityElement(children: .combine)\n            .accessibilityLabel(\"Analyzing pantry, please wait\")\n        }\n        \n        // Summary sheet\n        if let summary = viewModel.organizerSummary {\n            VStack {\n                // Summary content\n            }\n            .accessibilityElement(children: .combine)\n            .accessibilityLabel(\"Pantry organization complete\")\n            .accessibilityValue(\"Cleaned \\(summary.updatedCount) items, merged \\(summary.mergedCount) duplicates, removed \\(summary.removedCount) non-food items\")\n        }\n    }\n}\n```", "testStrategy": "1. Test with VoiceOver to ensure all elements are properly labeled\n2. Verify dynamic type support for text scaling\n3. Test color contrast meets WCAG standards\n4. Verify keyboard navigation works correctly\n5. Test with accessibility inspector in Xcode\n6. Conduct user testing with assistive technologies", "priority": "medium", "dependencies": [29, 33, 36], "status": "pending", "subtasks": []}, {"id": 40, "title": "Implement Telemetry for Usage Analytics", "description": "Add optional telemetry to track usage patterns and error rates for the new scanning and organization features.", "details": "1. Create a TelemetryService to track key events\n2. Add tracking for scan counts, image processing, and organization runs\n3. Track success/failure rates and performance metrics\n4. Implement privacy-respecting analytics that don't capture PII\n5. Make telemetry opt-in with clear user consent\n\n```swift\nclass TelemetryService {\n    private let analyticsProvider: AnalyticsProvider\n    private let isEnabled: Bool\n    \n    init(analyticsProvider: AnalyticsProvider, isEnabled: Bool = true) {\n        self.analyticsProvider = analyticsProvider\n        self.isEnabled = isEnabled\n    }\n    \n    // MARK: - Scan Events\n    \n    func trackScanStarted(imageCount: Int) {\n        guard isEnabled else { return }\n        analyticsProvider.logEvent(\"scan_started\", parameters: [\n            \"image_count\": imageCount\n        ])\n    }\n    \n    func trackScanCompleted(imageCount: Int, successCount: Int, duration: TimeInterval, ingredientCount: Int) {\n        guard isEnabled else { return }\n        analyticsProvider.logEvent(\"scan_completed\", parameters: [\n            \"image_count\": imageCount,\n            \"success_count\": successCount,\n            \"duration_seconds\": Int(duration),\n            \"ingredient_count\": ingredientCount\n        ])\n    }\n    \n    func trackScanError(imageCount: Int, errorType: String) {\n        guard isEnabled else { return }\n        analyticsProvider.logEvent(\"scan_error\", parameters: [\n            \"image_count\": imageCount,\n            \"error_type\": errorType\n        ])\n    }\n    \n    func trackAddToPantry(ingredientCount: Int) {\n        guard isEnabled else { return }\n        analyticsProvider.logEvent(\"add_to_pantry\", parameters: [\n            \"ingredient_count\": ingredientCount\n        ])\n    }\n    \n    // MARK: - Organizer Events\n    \n    func trackOrganizerStarted(itemCount: Int) {\n        guard isEnabled else { return }\n        analyticsProvider.logEvent(\"organizer_started\", parameters: [\n            \"item_count\": itemCount\n        ])\n    }\n    \n    func trackOrganizerCompleted(itemCount: Int, updatedCount: Int, mergedCount: Int, removedCount: Int, duration: TimeInterval) {\n        guard isEnabled else { return }\n        analyticsProvider.logEvent(\"organizer_completed\", parameters: [\n            \"item_count\": itemCount,\n            \"updated_count\": updatedCount,\n            \"merged_count\": mergedCount,\n            \"removed_count\": removedCount,\n            \"duration_seconds\": Int(duration)\n        ])\n    }\n    \n    func trackOrganizerError(itemCount: Int, errorType: String) {\n        guard isEnabled else { return }\n        analyticsProvider.logEvent(\"organizer_error\", parameters: [\n            \"item_count\": itemCount,\n            \"error_type\": errorType\n        ])\n    }\n    \n    // MARK: - Performance Metrics\n    \n    func trackVisionPerformance(imageCount: Int, duration: TimeInterval) {\n        guard isEnabled else { return }\n        analyticsProvider.logEvent(\"vision_performance\", parameters: [\n            \"image_count\": imageCount,\n            \"duration_seconds\": Int(duration)\n        ])\n    }\n    \n    func trackGeminiPerformance(inputSize: Int, duration: TimeInterval) {\n        guard isEnabled else { return }\n        analyticsProvider.logEvent(\"gemini_performance\", parameters: [\n            \"input_size\": inputSize,\n            \"duration_seconds\": Int(duration)\n        ])\n    }\n}\n\n// Protocol for analytics providers\nprotocol AnalyticsProvider {\n    func logEvent(_ name: String, parameters: [String: Any]?)\n}\n```\n\n```swift\n// Example integration in StagingViewModel\nextension StagingViewModel {\n    func processImages() async {\n        guard !selectedImages.isEmpty else { return }\n        \n        let startTime = Date()\n        let imageCount = selectedImages.count\n        \n        // Track scan started\n        telemetryService.trackScanStarted(imageCount: imageCount)\n        \n        await MainActor.run { isProcessing = true }\n        \n        do {\n            // Vision processing with timing\n            let visionStartTime = Date()\n            let visionOutputs = try await visionService.batchAnalyze(images: selectedImages)\n            let visionDuration = Date().timeIntervalSince(visionStartTime)\n            \n            // Track Vision performance\n            telemetryService.trackVisionPerformance(imageCount: imageCount, duration: visionDuration)\n            \n            // Gemini processing with timing\n            let geminiStartTime = Date()\n            let allowedCategories = PantryCategory.allCases.map { $0.rawValue }\n            let ingredients = try await geminiService.canonicalizeIngredients(\n                visionOutputs: visionOutputs,\n                allowedCategories: allowedCategories\n            )\n            let geminiDuration = Date().timeIntervalSince(geminiStartTime)\n            \n            // Track Gemini performance\n            telemetryService.trackGeminiPerformance(\n                inputSize: visionOutputs.reduce(0) { $0 + $1.ocrText.count },\n                duration: geminiDuration\n            )\n            \n            // Track overall scan completion\n            let totalDuration = Date().timeIntervalSince(startTime)\n            telemetryService.trackScanCompleted(\n                imageCount: imageCount,\n                successCount: visionOutputs.count,\n                duration: totalDuration,\n                ingredientCount: ingredients.count\n            )\n            \n            // Navigate to results\n            await MainActor.run {\n                isProcessing = false\n                navigationCoordinator.navigateToResults(with: ingredients)\n            }\n        } catch {\n            // Track error\n            telemetryService.trackScanError(\n                imageCount: imageCount,\n                errorType: String(describing: type(of: error))\n            )\n            \n            await MainActor.run {\n                isProcessing = false\n                processingError = error\n            }\n        }\n    }\n}\n```", "testStrategy": "1. Unit test telemetry event tracking with mock analytics provider\n2. Verify privacy compliance by ensuring no PII is captured\n3. Test opt-in/opt-out functionality\n4. Verify all key events are tracked correctly\n5. Test performance metrics accuracy\n6. Confirm telemetry doesn't impact app performance", "priority": "low", "dependencies": [32, 35], "status": "pending", "subtasks": []}, {"id": 41, "title": "Comprehensive Integration Testing", "description": "Implement end-to-end integration tests for the complete scanning and organization flows to ensure all components work together correctly.", "details": "1. Create integration tests for the full scan pipeline\n2. Test the complete pantry organization flow\n3. Implement mock services for Vision and Gemini APIs\n4. Test error handling and recovery across the full flows\n5. Verify data consistency throughout the process\n\n```swift\nclass ScanIntegrationTests: XCTestCase {\n    var app: XCUIApplication!\n    var mockVisionService: MockGoogleVisionAPIService!\n    var mockGeminiService: MockGeminiAPIService!\n    var mockPantryService: MockPantryService!\n    \n    override func setUp() {\n        super.setUp()\n        continueAfterFailure = false\n        app = XCUIApplication()\n        \n        // Setup mocks\n        mockVisionService = MockGoogleVisionAPIService()\n        mockGeminiService = MockGeminiAPIService()\n        mockPantryService = MockPantryService()\n        \n        // Inject mocks via launch arguments\n        app.launchArguments = [\"--uitesting\", \"--mock-services\"]\n        app.launch()\n    }\n    \n    func testFullScanPipeline() {\n        // Setup mock responses\n        mockVisionService.mockVisionOutputs = [\n            VisionOutput(ocrText: \"Organic Whole Milk\", labels: [(\"milk\", 0.95), (\"dairy\", 0.9)]),\n            VisionOutput(ocrText: \"Extra Virgin Olive Oil 500ml\", labels: [(\"oil\", 0.9), (\"cooking\", 0.8)])\n        ]\n        \n        mockGeminiService.mockIngredients = [\n            Ingredient(id: UUID(), name: \"Whole Milk\", category: .dairy),\n            Ingredient(id: UUID(), name: \"Olive Oil\", category: .oilsVinegarsCondiments)\n        ]\n        \n        // Navigate to Scan tab\n        app.tabBars.buttons[\"Scan\"].tap()\n        \n        // Add images from library\n        app.buttons[\"Choose from Library\"].tap()\n        \n        // Mock photo selection\n        // Note: In real tests, you'd need to handle the system photo picker\n        // For this example, we'll assume the mock injection happens after this step\n        \n        // Process images\n        app.buttons[\"Process\"].tap()\n        \n        // Verify processing overlay appears\n        XCTAssertTrue(app.staticTexts[\"Processing...\"].exists)\n        \n        // Wait for results screen\n        let resultsTitle = app.staticTexts[\"Scan Results\"]\n        XCTAssertTrue(resultsTitle.waitForExistence(timeout: 5))\n        \n        // Verify ingredients are displayed\n        XCTAssertTrue(app.staticTexts[\"Whole Milk\"].exists)\n        XCTAssertTrue(app.staticTexts[\"Olive Oil\"].exists)\n        \n        // Verify categories are correct\n        XCTAssertTrue(app.staticTexts[\"Dairy\"].exists)\n        XCTAssertTrue(app.staticTexts[\"Oils, Vinegars & Condiments\"].exists)\n        \n        // Add to pantry\n        app.buttons[\"Add Selected to Pantry\"].tap()\n        \n        // Verify navigation to Pantry tab\n        XCTAssertTrue(app.tabBars.buttons[\"Pantry\"].isSelected)\n        \n        // Verify items were added to pantry\n        XCTAssertTrue(app.staticTexts[\"Whole Milk\"].exists)\n        XCTAssertTrue(app.staticTexts[\"Olive Oil\"].exists)\n    }\n    \n    func testPantryOrganization() {\n        // Setup mock pantry items\n        mockPantryService.mockPantryItems = [\n            Ingredient(id: UUID(), name: \"Whole Milk 1 gallon\", category: .other),\n            Ingredient(id: UUID(), name: \"Organic Whole Milk\", category: .dairy),\n            Ingredient(id: UUID(), name: \"EVOO 500ml\", category: .other),\n            Ingredient(id: UUID(), name: \"Non-food item\", category: .other)\n        ]\n        \n        // Setup mock organizer response\n        let mockCleanUpPlan = CleanUpPlan(\n            updates: [\n                UpdateOp(id: mockPantryService.mockPantryItems[0].id, newName: \"Whole Milk\", newCategory: .dairy),\n                UpdateOp(id: mockPantryService.mockPantryItems[2].id, newName: \"Olive Oil\", newCategory: .oilsVinegarsCondiments)\n            ],\n            removals: [mockPantryService.mockPantryItems[3].id],\n            merges: [\n                MergeOp(winnerId: mockPantryService.mockPantryItems[1].id, loserIds: [mockPantryService.mockPantryItems[0].id])\n            ]\n        )\n        \n        mockGeminiService.mockCleanUpPlan = mockCleanUpPlan\n        \n        // Navigate to Pantry tab\n        app.tabBars.buttons[\"Pantry\"].tap()\n        \n        // Tap Organize Pantry\n        app.buttons[\"Organize Pantry\"].tap()\n        \n        // Verify organizing overlay appears\n        XCTAssertTrue(app.staticTexts[\"Analyzing pantry...\"].exists)\n        \n        // Wait for summary\n        let summaryTitle = app.staticTexts[\"Pantry Organization Complete\"]\n        XCTAssertTrue(summaryTitle.waitForExistence(timeout: 5))\n        \n        // Verify summary counts\n        XCTAssertTrue(app.staticTexts[\"Cleaned: 2 items\"].exists)\n        XCTAssertTrue(app.staticTexts[\"Merged: 1 duplicates\"].exists)\n        XCTAssertTrue(app.staticTexts[\"Removed: 1 non-food items\"].exists)\n        \n        // Dismiss summary\n        app.buttons[\"Done\"].tap()\n        \n        // Verify pantry is updated\n        XCTAssertTrue(app.staticTexts[\"Whole Milk\"].exists)\n        XCTAssertTrue(app.staticTexts[\"Olive Oil\"].exists)\n        XCTAssertFalse(app.staticTexts[\"Non-food item\"].exists)\n    }\n}\n```", "testStrategy": "1. Run integration tests on both simulator and real devices\n2. Test with various mock data sets to cover different scenarios\n3. Verify error handling by injecting mock errors\n4. Test performance with large data sets\n5. Verify UI state consistency throughout flows\n6. Test with accessibility features enabled", "priority": "high", "dependencies": [32, 33, 35, 36, 37, 38], "status": "pending", "subtasks": []}], "metadata": {"created": "2025-08-08T06:08:29.802Z", "updated": "2025-08-16T19:33:27.853Z", "description": "Tasks for master context - Task 35 PantryOrganizerService completed"}}}